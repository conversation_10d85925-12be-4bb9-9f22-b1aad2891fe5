import { useState, useEffect } from 'react';
import { X, <PERSON>, Trophy, <PERSON>, <PERSON>, Flame } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Notification {
  id: string;
  type: 'track_added' | 'track_played' | 'vote_cast' | 'user_joined' | 'achievement';
  title: string;
  message: string;
  icon: React.ReactNode;
  timestamp: Date;
  autoHide?: boolean;
}

interface LiveNotificationsProps {
  socket?: any;
}

export function LiveNotifications({ socket }: LiveNotificationsProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    if (!socket) return;

    // Écouter les événements WebSocket
    socket.on('track-added', (data: any) => {
      addNotification({
        type: 'track_added',
        title: 'Nouvelle piste ajoutée',
        message: `${data.userName} a ajouté "${data.trackName}"`,
        icon: <Music className="h-4 w-4" />,
        autoHide: true,
      });
    });

    socket.on('track-played', (data: any) => {
      addNotification({
        type: 'track_played',
        title: 'Lecture en cours',
        message: `"${data.trackName}" est maintenant en lecture`,
        icon: <Star className="h-4 w-4" />,
        autoHide: true,
      });
    });

    socket.on('vote-cast', (data: any) => {
      addNotification({
        type: 'vote_cast',
        title: 'Nouveau vote',
        message: `${data.userName} a voté ${data.emoji} pour "${data.trackName}"`,
        icon: <Flame className="h-4 w-4" />,
        autoHide: true,
      });
    });

    socket.on('user-joined', (data: any) => {
      addNotification({
        type: 'user_joined',
        title: 'Nouveau participant',
        message: `${data.userName} a rejoint la session`,
        icon: <Users className="h-4 w-4" />,
        autoHide: true,
      });
    });

    socket.on('achievement-unlocked', (data: any) => {
      addNotification({
        type: 'achievement',
        title: 'Succès débloqué !',
        message: `${data.userName} a débloqué "${data.achievementName}"`,
        icon: <Trophy className="h-4 w-4" />,
        autoHide: false, // Les succès restent visibles plus longtemps
      });
    });

    return () => {
      socket.off('track-added');
      socket.off('track-played');
      socket.off('vote-cast');
      socket.off('user-joined');
      socket.off('achievement-unlocked');
    };
  }, [socket]);

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
    };

    setNotifications(prev => [newNotification, ...prev.slice(0, 4)]); // Garder max 5 notifications

    // Auto-hide après 5 secondes si autoHide est true
    if (notification.autoHide) {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, 5000);
    }
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'track_added':
        return 'border-blue-500 bg-blue-900/20';
      case 'track_played':
        return 'border-green-500 bg-green-900/20';
      case 'vote_cast':
        return 'border-orange-500 bg-orange-900/20';
      case 'user_joined':
        return 'border-purple-500 bg-purple-900/20';
      case 'achievement':
        return 'border-yellow-500 bg-yellow-900/20';
      default:
        return 'border-gray-500 bg-gray-900/20';
    }
  };

  const getIconColor = (type: Notification['type']) => {
    switch (type) {
      case 'track_added':
        return 'text-blue-400';
      case 'track_played':
        return 'text-green-400';
      case 'vote_cast':
        return 'text-orange-400';
      case 'user_joined':
        return 'text-purple-400';
      case 'achievement':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  if (notifications.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`border-l-4 rounded-lg p-4 bg-gray-800/95 backdrop-blur-sm shadow-lg transition-all duration-300 ${getNotificationColor(notification.type)}`}
        >
          <div className="flex items-start space-x-3">
            <div className={`flex-shrink-0 ${getIconColor(notification.type)}`}>
              {notification.icon}
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-semibold text-white">
                {notification.title}
              </h4>
              <p className="text-sm text-gray-300 mt-1">
                {notification.message}
              </p>
              <p className="text-xs text-gray-500 mt-2">
                {notification.timestamp.toLocaleTimeString()}
              </p>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => removeNotification(notification.id)}
              className="flex-shrink-0 h-6 w-6 p-0 text-gray-400 hover:text-white"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
}
