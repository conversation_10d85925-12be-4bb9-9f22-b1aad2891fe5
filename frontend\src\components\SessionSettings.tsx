import { useState } from 'react';
import { Settings, Volume2, Users, Clock, Shuffle, Repeat } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';

interface SessionSettingsProps {
  session: {
    id: string;
    settings: {
      allowSubmissions: boolean;
      votingEnabled: boolean;
      maxTracksPerUser: number;
    };
  };
  isHost: boolean;
  onSettingsChange?: (settings: any) => void;
}

export function SessionSettings({ session, isHost, onSettingsChange }: SessionSettingsProps) {
  const [settings, setSettings] = useState(session.settings);
  const [playbackSettings, setPlaybackSettings] = useState({
    autoPlay: true,
    shuffleMode: false,
    repeatMode: false,
    crossfade: 0,
    volume: 70,
  });

  const updateSetting = (key: string, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    if (onSettingsChange) {
      onSettingsChange(newSettings);
    }
  };

  const updatePlaybackSetting = (key: string, value: any) => {
    setPlaybackSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <h3 className="font-semibold mb-4 flex items-center">
        <Settings className="h-5 w-5 mr-2 text-gray-400" />
        Paramètres de session
      </h3>

      <div className="space-y-4">
        {/* Paramètres de session */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-300">Session</h4>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-gray-400" />
              <span className="text-sm">Autoriser les ajouts</span>
            </div>
            <Switch
              checked={settings.allowSubmissions}
              onCheckedChange={(checked) => updateSetting('allowSubmissions', checked)}
              disabled={!isHost}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm">Système de votes</span>
            </div>
            <Switch
              checked={settings.votingEnabled}
              onCheckedChange={(checked) => updateSetting('votingEnabled', checked)}
              disabled={!isHost}
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Pistes max par utilisateur</span>
              <span className="text-sm text-gray-400">{settings.maxTracksPerUser}</span>
            </div>
            <Slider
              value={[settings.maxTracksPerUser]}
              onValueChange={([value]) => updateSetting('maxTracksPerUser', value)}
              max={20}
              min={1}
              step={1}
              disabled={!isHost}
              className="w-full"
            />
          </div>
        </div>

        {/* Paramètres de lecture (Hôte uniquement) */}
        {isHost && (
          <div className="space-y-3 border-t border-gray-700 pt-4">
            <h4 className="text-sm font-medium text-gray-300">Lecture</h4>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-400" />
                <span className="text-sm">Lecture automatique</span>
              </div>
              <Switch
                checked={playbackSettings.autoPlay}
                onCheckedChange={(checked) => updatePlaybackSetting('autoPlay', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Shuffle className="h-4 w-4 text-gray-400" />
                <span className="text-sm">Mode aléatoire</span>
              </div>
              <Switch
                checked={playbackSettings.shuffleMode}
                onCheckedChange={(checked) => updatePlaybackSetting('shuffleMode', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Repeat className="h-4 w-4 text-gray-400" />
                <span className="text-sm">Répétition</span>
              </div>
              <Switch
                checked={playbackSettings.repeatMode}
                onCheckedChange={(checked) => updatePlaybackSetting('repeatMode', checked)}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Volume2 className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">Volume</span>
                </div>
                <span className="text-sm text-gray-400">{playbackSettings.volume}%</span>
              </div>
              <Slider
                value={[playbackSettings.volume]}
                onValueChange={([value]) => updatePlaybackSetting('volume', value)}
                max={100}
                min={0}
                step={5}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Fondu enchaîné</span>
                <span className="text-sm text-gray-400">{playbackSettings.crossfade}s</span>
              </div>
              <Slider
                value={[playbackSettings.crossfade]}
                onValueChange={([value]) => updatePlaybackSetting('crossfade', value)}
                max={12}
                min={0}
                step={1}
                className="w-full"
              />
            </div>
          </div>
        )}

        {/* Actions */}
        {isHost && (
          <div className="border-t border-gray-700 pt-4">
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                className="flex-1"
                onClick={() => {
                  // Exporter la playlist vers Spotify
                  console.log('Export to Spotify');
                }}
              >
                Exporter vers Spotify
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="flex-1"
                onClick={() => {
                  // Partager la session
                  navigator.clipboard.writeText(window.location.href);
                }}
              >
                Partager
              </Button>
            </div>
          </div>
        )}

        {/* Informations de session */}
        <div className="border-t border-gray-700 pt-4 text-xs text-gray-500">
          <p>Session ID: {session.id.slice(0, 8)}...</p>
          <p>Créée: {new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </div>
  );
}
