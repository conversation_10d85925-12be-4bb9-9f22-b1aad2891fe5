import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Music, Users, UserPlus, ArrowLeft } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

export default function GuestJoinPage() {
  const [sessionCode, setSessionCode] = useState('');
  const [guestName, setGuestName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleJoinAsGuest = async () => {
    if (!sessionCode.trim()) {
      toast({
        title: "Code requis",
        description: "Veuillez entrer un code de session",
        variant: "destructive",
      });
      return;
    }

    if (!guestName.trim()) {
      toast({
        title: "Nom requis",
        description: "Veuillez entrer votre nom",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Stocker les informations d'invité dans le localStorage
      localStorage.setItem('guestInfo', JSON.stringify({
        name: guestName.trim(),
        sessionCode: sessionCode.toUpperCase(),
        isGuest: true,
        joinedAt: new Date().toISOString()
      }));
      
      navigate(`/session/${sessionCode.toUpperCase()}?guest=true`);
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: "Impossible de rejoindre la session",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <Button
          onClick={() => navigate('/')}
          variant="ghost"
          className="text-white hover:bg-white/10 mb-8"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour à l'accueil
        </Button>

        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <UserPlus className="h-16 w-16 text-gray-400 mr-4" />
            <h1 className="text-5xl font-bold text-white">Accès Invité</h1>
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Rejoignez une session rapidement sans créer de compte
          </p>
        </div>

        {/* Join Form */}
        <div className="max-w-md mx-auto">
          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Users className="mr-2 h-5 w-5" />
                Rejoindre en tant qu'invité
              </CardTitle>
              <CardDescription className="text-gray-400">
                Entrez vos informations pour participer à la session
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-300 mb-2 block">
                  Votre nom
                </label>
                <Input
                  placeholder="Comment vous appelez-vous ?"
                  value={guestName}
                  onChange={(e) => setGuestName(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                  maxLength={20}
                />
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-300 mb-2 block">
                  Code de session
                </label>
                <Input
                  placeholder="ex: ABC123"
                  value={sessionCode}
                  onChange={(e) => setSessionCode(e.target.value.toUpperCase())}
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                  maxLength={6}
                />
              </div>

              <Button
                onClick={handleJoinAsGuest}
                disabled={isLoading || !sessionCode.trim() || !guestName.trim()}
                className="w-full bg-gray-600 hover:bg-gray-700"
              >
                <UserPlus className="mr-2 h-4 w-4" />
                {isLoading ? 'Connexion...' : 'Rejoindre la session'}
              </Button>

              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <h4 className="text-yellow-400 font-medium mb-2">⚠️ Mode invité</h4>
                <ul className="text-sm text-yellow-200 space-y-1">
                  <li>• Vos points ne seront pas sauvegardés</li>
                  <li>• Vous ne pourrez pas créer de sessions</li>
                  <li>• Vos statistiques ne seront pas conservées</li>
                </ul>
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-400 mb-2">
                  Vous voulez garder vos points ?
                </p>
                <Button
                  onClick={() => navigate('/')}
                  variant="outline"
                  size="sm"
                  className="text-green-400 border-green-400 hover:bg-green-400/10"
                >
                  Se connecter avec Spotify
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Features for Guests */}
        <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto mt-16">
          <div className="text-center">
            <Music className="h-12 w-12 text-purple-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">Ajoutez des morceaux</h3>
            <p className="text-gray-400">
              Recherchez et proposez vos titres préférés à la playlist
            </p>
          </div>
          <div className="text-center">
            <Users className="h-12 w-12 text-blue-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">Votez pour les pistes</h3>
            <p className="text-gray-400">
              Influencez l'ordre de lecture en votant pour vos morceaux favoris
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
