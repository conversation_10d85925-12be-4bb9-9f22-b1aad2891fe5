import { Router } from 'express';
import { AuthController } from '../controllers/auth.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = Router();

// Public routes
router.get('/spotify/login', AuthController.spotifyLogin);
router.get('/spotify/callback', AuthController.spotifyCallback);

// Protected routes
router.get('/spotify/token', authMiddleware, AuthController.getSpotifyToken);
router.post('/refresh-token', authMiddleware, AuthController.refreshToken);
router.get('/me', authMiddleware, AuthController.getCurrentUser);
router.post('/logout', authMiddleware, AuthController.logout);

export default router;
