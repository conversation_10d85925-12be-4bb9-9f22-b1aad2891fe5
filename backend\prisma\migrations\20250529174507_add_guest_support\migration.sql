/*
  Warnings:

  - A unique constraint covering the columns `[sessionId,guestId]` on the table `SessionParticipant` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "SessionParticipant" DROP CONSTRAINT "SessionParticipant_userId_fkey";

-- AlterTable
ALTER TABLE "SessionParticipant" ADD COLUMN     "guestId" TEXT,
ALTER COLUMN "userId" DROP NOT NULL;

-- CreateIndex
CREATE INDEX "SessionParticipant_guestId_idx" ON "SessionParticipant"("guestId");

-- CreateIndex
CREATE UNIQUE INDEX "SessionParticipant_sessionId_guestId_key" ON "SessionParticipant"("sessionId", "guestId");

-- AddForeignKey
ALTER TABLE "SessionParticipant" ADD CONSTRAINT "SessionParticipant_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
