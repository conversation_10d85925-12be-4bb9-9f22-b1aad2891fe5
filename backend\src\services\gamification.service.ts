import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class GamificationService {
  // Badge definitions
  private static readonly BADGES = {
    FIRST_TRACK: {
      name: 'First Track',
      description: 'Added your first track to a session',
      icon: '🎵',
      points: 10,
    },
    POPULAR_CHOICE: {
      name: 'Popular Choice',
      description: 'Your track received 10+ upvotes',
      icon: '⭐',
      points: 20,
    },
    PLAYLIST_MASTER: {
      name: 'Playlist Master',
      description: 'Added 50 tracks across all sessions',
      icon: '🎼',
      points: 50,
    },
    SOCIAL_BUTTERFLY: {
      name: 'Social Butterfly',
      description: 'Participated in 10 different sessions',
      icon: '🦋',
      points: 30,
    },
    TRENDSETTER: {
      name: 'Trendsetter',
      description: 'First to add a track that became #1',
      icon: '🚀',
      points: 40,
    },
    PARTY_STARTER: {
      name: 'Party Starter',
      description: 'Created your first session',
      icon: '🎉',
      points: 15,
    },
    VOTE_CHAMPION: {
      name: 'Vote Champion',
      description: 'Voted on 100 tracks',
      icon: '🗳️',
      points: 25,
    },
    MUSIC_EXPLORER: {
      name: 'Music Explorer',
      description: 'Added tracks from 20 different artists',
      icon: '🔍',
      points: 35,
    },
  };

  /**
   * Initialize badges in database
   */
  static async initializeBadges(): Promise<void> {
    for (const [_key, badge] of Object.entries(this.BADGES)) {
      await prisma.badge.upsert({
        where: { name: badge.name },
        update: {},
        create: badge,
      });
    }
  }

  /**
   * Check and award badges based on user activity
   */
  static async checkAndAwardBadges(userId: string): Promise<string[]> {
    const awardedBadges: string[] = [];

    // Get user stats
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        badges: {
          include: {
            badge: true,
          },
        },
        gameEvents: true,
      },
    });

    if (!user) return awardedBadges;

    const existingBadgeNames = user.badges.map(ub => ub.badge.name);

    // Check First Track badge
    if (!existingBadgeNames.includes(this.BADGES.FIRST_TRACK.name)) {
      const trackAddedEvents = user.gameEvents.filter(e => e.type === 'TRACK_ADDED');
      if (trackAddedEvents.length >= 1) {
        await this.awardBadge(userId, this.BADGES.FIRST_TRACK.name);
        awardedBadges.push(this.BADGES.FIRST_TRACK.name);
      }
    }

    // Check Party Starter badge
    if (!existingBadgeNames.includes(this.BADGES.PARTY_STARTER.name)) {
      const sessionCreatedEvents = user.gameEvents.filter(e => e.type === 'SESSION_CREATED');
      if (sessionCreatedEvents.length >= 1) {
        await this.awardBadge(userId, this.BADGES.PARTY_STARTER.name);
        awardedBadges.push(this.BADGES.PARTY_STARTER.name);
      }
    }

    // Check Social Butterfly badge
    if (!existingBadgeNames.includes(this.BADGES.SOCIAL_BUTTERFLY.name)) {
      const uniqueSessions = new Set(
        user.gameEvents
          .filter(e => e.sessionId)
          .map(e => e.sessionId)
      ).size;
      if (uniqueSessions >= 10) {
        await this.awardBadge(userId, this.BADGES.SOCIAL_BUTTERFLY.name);
        awardedBadges.push(this.BADGES.SOCIAL_BUTTERFLY.name);
      }
    }

    // Check Playlist Master badge
    if (!existingBadgeNames.includes(this.BADGES.PLAYLIST_MASTER.name)) {
      const trackCount = user.gameEvents.filter(e => e.type === 'TRACK_ADDED').length;
      if (trackCount >= 50) {
        await this.awardBadge(userId, this.BADGES.PLAYLIST_MASTER.name);
        awardedBadges.push(this.BADGES.PLAYLIST_MASTER.name);
      }
    }

    // Check Vote Champion badge
    if (!existingBadgeNames.includes(this.BADGES.VOTE_CHAMPION.name)) {
      const voteCount = await prisma.vote.count({
        where: {
          user: {
            userId: userId,
          },
        },
      });
      if (voteCount >= 100) {
        await this.awardBadge(userId, this.BADGES.VOTE_CHAMPION.name);
        awardedBadges.push(this.BADGES.VOTE_CHAMPION.name);
      }
    }

    return awardedBadges;
  }

  /**
   * Award a badge to a user
   */
  private static async awardBadge(userId: string, badgeName: string): Promise<void> {
    const badge = await prisma.badge.findUnique({
      where: { name: badgeName },
    });

    if (!badge) return;

    // Create user badge
    await prisma.userBadge.create({
      data: {
        userId,
        badgeId: badge.id,
      },
    });

    // Track game event
    await prisma.gameEvent.create({
      data: {
        userId,
        type: 'BADGE_EARNED',
        points: badge.points,
        metadata: { badgeName: badge.name },
      },
    });

    // Update user points
    await prisma.user.update({
      where: { id: userId },
      data: {
        points: { increment: badge.points },
      },
    });
  }

  /**
   * Calculate user level based on points
   */
  static calculateLevel(points: number): number {
    // Level progression: 0-99 = Level 1, 100-249 = Level 2, etc.
    if (points < 100) return 1;
    if (points < 250) return 2;
    if (points < 500) return 3;
    if (points < 1000) return 4;
    if (points < 2000) return 5;
    if (points < 3500) return 6;
    if (points < 5500) return 7;
    if (points < 8000) return 8;
    if (points < 11000) return 9;
    return 10;
  }

  /**
   * Update user level if needed
   */
  static async updateUserLevel(userId: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) return false;

    const newLevel = this.calculateLevel(user.points);
    if (newLevel !== user.level) {
      await prisma.user.update({
        where: { id: userId },
        data: { level: newLevel },
      });

      // Track milestone event
      if (newLevel > user.level) {
        await prisma.gameEvent.create({
          data: {
            userId,
            type: 'MILESTONE_REACHED',
            points: 0,
            metadata: { level: newLevel },
          },
        });
      }

      return true;
    }

    return false;
  }

  /**
   * Get leaderboard
   */
  static async getLeaderboard(limit: number = 10): Promise<any[]> {
    const users = await prisma.user.findMany({
      take: limit,
      orderBy: [
        { points: 'desc' },
        { level: 'desc' },
      ],
      select: {
        id: true,
        displayName: true,
        avatarUrl: true,
        points: true,
        level: true,
        badges: {
          include: {
            badge: true,
          },
        },
      },
    });

    return users.map((user, index) => ({
      rank: index + 1,
      ...user,
      badges: user.badges.map(ub => ub.badge),
    }));
  }

  /**
   * Get user stats
   */
  static async getUserStats(userId: string): Promise<any> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        badges: {
          include: {
            badge: true,
          },
        },
        gameEvents: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
        participations: {
          include: {
            session: true,
          },
        },
      },
    });

    if (!user) return null;

    const stats = {
      totalPoints: user.points,
      level: user.level,
      nextLevelPoints: this.getPointsForNextLevel(user.points),
      badges: user.badges.map(ub => ub.badge),
      recentActivity: user.gameEvents,
      sessionsJoined: user.participations.length,
      tracksAdded: user.gameEvents.filter(e => e.type === 'TRACK_ADDED').length,
      votesGiven: await prisma.vote.count({
        where: {
          user: {
            userId: userId,
          },
        },
      }),
    };

    return stats;
  }

  /**
   * Get points needed for next level
   */
  private static getPointsForNextLevel(currentPoints: number): number {
    const levels = [0, 100, 250, 500, 1000, 2000, 3500, 5500, 8000, 11000];
    const currentLevel = this.calculateLevel(currentPoints);
    
    if (currentLevel >= 10) return 0;
    
    return levels[currentLevel] - currentPoints;
  }
}
