// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id        String   @id @default(uuid())
  code      String   @unique @db.VarChar(6)
  name      String
  hostId    String
  isActive  Boolean  @default(true)
  settings  Json     @default("{\"allowSubmissions\": true, \"votingEnabled\": true, \"maxTracksPerUser\": 10}")
  createdAt DateTime @default(now())
  expiresAt DateTime

  // Relations
  host         User             @relation("HostSessions", fields: [hostId], references: [id])
  participants SessionParticipant[]
  tracks       Track[]

  @@index([code])
  @@index([hostId])
}

model User {
  id           String   @id @default(uuid())
  spotifyId    String   @unique
  email        String   @unique
  displayName  String
  avatarUrl    String?
  refreshToken String?
  points       Int      @default(0)
  level        Int      @default(1)
  createdAt    DateTime @default(now())
  lastLoginAt  DateTime @default(now())

  // Relations
  hostedSessions Session[]           @relation("HostSessions")
  participations SessionParticipant[]
  badges         UserBadge[]
  gameEvents     GameEvent[]

  @@index([spotifyId])
  @@index([email])
}

model SessionParticipant {
  id                String   @id @default(uuid())
  sessionId         String
  userId            String?  // Optional for guests
  guestId           String?  // For guest users
  name              String   // Display name for this session
  role              UserRole @default(PARTICIPANT)
  points            Int      @default(0)
  joinedAt          DateTime @default(now())

  // Relations
  session         Session      @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user            User?        @relation(fields: [userId], references: [id])
  submittedTracks Track[]
  votes           Vote[]

  @@unique([sessionId, userId])
  @@unique([sessionId, guestId])
  @@index([sessionId])
  @@index([userId])
  @@index([guestId])
}

model Track {
  id          String   @id @default(uuid())
  sessionId   String
  spotifyId   String
  submittedBy String
  title       String
  artist      String
  album       String?
  duration    Int      // in milliseconds
  imageUrl    String?
  position    Int      @default(0)
  score       Int      @default(0)
  isPlaying   Boolean  @default(false)
  hasPlayed   Boolean  @default(false)
  addedAt     DateTime @default(now())

  // Relations
  session   Session            @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  submitter SessionParticipant @relation(fields: [submittedBy], references: [id])
  votes     Vote[]

  @@unique([sessionId, spotifyId])
  @@index([sessionId])
  @@index([submittedBy])
}

model Vote {
  id        String   @id @default(uuid())
  trackId   String
  userId    String
  emoji     String
  value     Int      // -1, 0, or 1
  createdAt DateTime @default(now())

  // Relations
  track Track              @relation(fields: [trackId], references: [id], onDelete: Cascade)
  user  SessionParticipant @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([trackId, userId, emoji])
  @@index([trackId])
  @@index([userId])
}

model Badge {
  id          String   @id @default(uuid())
  name        String   @unique
  description String
  icon        String
  points      Int      @default(0)
  createdAt   DateTime @default(now())

  // Relations
  users UserBadge[]
}

model UserBadge {
  id        String   @id @default(uuid())
  userId    String
  badgeId   String
  earnedAt  DateTime @default(now())

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  badge Badge @relation(fields: [badgeId], references: [id])

  @@unique([userId, badgeId])
  @@index([userId])
  @@index([badgeId])
}

model GameEvent {
  id        String        @id @default(uuid())
  userId    String
  sessionId String?
  type      GameEventType
  points    Int
  metadata  Json?
  createdAt DateTime      @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([sessionId])
  @@index([type])
}

// Enums
enum UserRole {
  HOST
  PARTICIPANT
}

enum GameEventType {
  TRACK_ADDED
  TRACK_UPVOTED
  TRACK_PLAYED
  BADGE_EARNED
  MILESTONE_REACHED
  SESSION_CREATED
  SESSION_JOINED
}
