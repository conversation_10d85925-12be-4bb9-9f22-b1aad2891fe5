import { useState, useEffect } from 'react';
import { Trophy, Music, Users, TrendingUp, <PERSON>, Flame } from 'lucide-react';

interface SessionStatsProps {
  session: {
    id: string;
    participants: Array<{
      id: string;
      name: string;
      points: number;
      user: {
        displayName: string;
      };
    }>;
    tracks: Array<{
      id: string;
      score: number;
      hasPlayed: boolean;
      submitter: {
        user: {
          displayName: string;
        };
      };
    }>;
  };
}

export function SessionStats({ session }: SessionStatsProps) {
  const [stats, setStats] = useState({
    totalTracks: 0,
    totalVotes: 0,
    topContributor: '',
    averageScore: 0,
    playedTracks: 0,
  });

  useEffect(() => {
    // Calculer les statistiques
    const totalTracks = session.tracks.length;
    const totalVotes = session.tracks.reduce((sum, track) => sum + Math.abs(track.score), 0);
    const playedTracks = session.tracks.filter(track => track.hasPlayed).length;
    const averageScore = totalTracks > 0 ? totalVotes / totalTracks : 0;

    // Trouver le contributeur principal
    const contributorCounts = session.tracks.reduce((acc, track) => {
      const contributor = track.submitter.user.displayName;
      acc[contributor] = (acc[contributor] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topContributor = Object.entries(contributorCounts)
      .sort(([, a], [, b]) => b - a)[0]?.[0] || 'Aucun';

    setStats({
      totalTracks,
      totalVotes,
      topContributor,
      averageScore: Math.round(averageScore * 10) / 10,
      playedTracks,
    });
  }, [session]);

  // Leaderboard des participants
  const leaderboard = session.participants
    .sort((a, b) => b.points - a.points)
    .slice(0, 3);

  // Top tracks par score
  const topTracks = session.tracks
    .filter(track => !track.hasPlayed)
    .sort((a, b) => b.score - a.score)
    .slice(0, 3);

  return (
    <div className="space-y-4">
      {/* Statistiques générales */}
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="font-semibold mb-3 flex items-center">
          <TrendingUp className="h-5 w-5 mr-2 text-purple-400" />
          Statistiques
        </h3>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="bg-gray-700/50 p-3 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Pistes</span>
              <Music className="h-4 w-4 text-blue-400" />
            </div>
            <p className="text-lg font-bold">{stats.totalTracks}</p>
          </div>
          <div className="bg-gray-700/50 p-3 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Jouées</span>
              <Star className="h-4 w-4 text-green-400" />
            </div>
            <p className="text-lg font-bold">{stats.playedTracks}</p>
          </div>
          <div className="bg-gray-700/50 p-3 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Votes</span>
              <Flame className="h-4 w-4 text-orange-400" />
            </div>
            <p className="text-lg font-bold">{stats.totalVotes}</p>
          </div>
          <div className="bg-gray-700/50 p-3 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Score moy.</span>
              <TrendingUp className="h-4 w-4 text-purple-400" />
            </div>
            <p className="text-lg font-bold">{stats.averageScore}</p>
          </div>
        </div>
        {stats.topContributor !== 'Aucun' && (
          <div className="mt-3 p-3 bg-purple-900/30 rounded-lg">
            <p className="text-sm text-gray-400">Top contributeur</p>
            <p className="font-semibold text-purple-400">{stats.topContributor}</p>
          </div>
        )}
      </div>

      {/* Leaderboard */}
      {leaderboard.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="font-semibold mb-3 flex items-center">
            <Trophy className="h-5 w-5 mr-2 text-yellow-400" />
            Classement
          </h3>
          <div className="space-y-2">
            {leaderboard.map((participant, index) => (
              <div
                key={participant.id}
                className={`flex items-center space-x-3 p-2 rounded-lg ${
                  index === 0 ? 'bg-yellow-900/30' : 
                  index === 1 ? 'bg-gray-600/30' : 
                  'bg-orange-900/30'
                }`}
              >
                <span className={`text-lg font-bold ${
                  index === 0 ? 'text-yellow-400' : 
                  index === 1 ? 'text-gray-400' : 
                  'text-orange-400'
                }`}>
                  {index + 1}
                </span>
                <span className="flex-1">{participant.user.displayName}</span>
                <span className="font-bold">{participant.points} pts</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Top tracks */}
      {topTracks.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="font-semibold mb-3 flex items-center">
            <Star className="h-5 w-5 mr-2 text-green-400" />
            Top pistes
          </h3>
          <div className="space-y-2">
            {topTracks.map((track, index) => (
              <div
                key={track.id}
                className="flex items-center space-x-3 p-2 rounded-lg bg-gray-700/30"
              >
                <span className="text-gray-400 font-bold">{index + 1}</span>
                <div className="flex-1 truncate">
                  <p className="truncate text-sm">{track.title}</p>
                  <p className="text-xs text-gray-500 truncate">
                    par {track.submitter.user.displayName}
                  </p>
                </div>
                <span className="text-green-400 font-bold">+{track.score}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
