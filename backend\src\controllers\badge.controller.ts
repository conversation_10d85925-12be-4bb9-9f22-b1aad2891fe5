import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class BadgeController {
  // Obtenir les statistiques d'un utilisateur
  static async getUserStats(req: Request & { user?: any }, res: Response): Promise<void> {
    try {
      const userId = req.params.userId || req.user?.userId;
      
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Récupérer les statistiques de l'utilisateur
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          participations: {
            include: {
              session: {
                include: {
                  tracks: true
                }
              },
              submittedTracks: {
                include: {
                  votes: true
                }
              }
            }
          },
          badges: {
            include: {
              badge: true
            }
          }
        }
      });

      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      // Calculer les statistiques
      const totalTracks = user.participations.reduce((sum, participation) =>
        sum + participation.submittedTracks.length, 0
      );
      const totalVotes = user.participations.reduce((sum, participation) =>
        sum + participation.submittedTracks.reduce((trackSum, track) =>
          trackSum + track.votes.length, 0
        ), 0
      );
      const totalPoints = user.participations.reduce((sum, participant) =>
        sum + participant.points, 0
      );
      const sessionsHosted = user.participations.filter(p =>
        p.session.hostId === userId
      ).length;
      const sessionsJoined = user.participations.length;
      const tracksPlayed = user.participations.reduce((sum, participation) =>
        sum + participation.submittedTracks.filter(track => track.hasPlayed).length, 0
      );

      // Pour l'instant, retourner des badges vides (à implémenter plus tard)
      const badges: any[] = [];

      const stats = {
        userId,
        totalTracks,
        totalVotes,
        totalPoints,
        sessionsHosted,
        sessionsJoined,
        tracksPlayed,
        badges
      };

      res.json(stats);
    } catch (error) {
      console.error('Error getting user stats:', error);
      res.status(500).json({ error: 'Failed to get user stats' });
    }
  }

  // Vérifier les nouveaux badges
  static async checkBadges(req: Request & { user?: any }, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Pour l'instant, retourner aucun nouveau badge
      // Cette logique sera implémentée plus tard
      const newBadges: any[] = [];

      res.json({ newBadges });
    } catch (error) {
      console.error('Error checking badges:', error);
      res.status(500).json({ error: 'Failed to check badges' });
    }
  }

  // Obtenir le leaderboard
  static async getLeaderboard(req: Request, res: Response): Promise<void> {
    try {
      // Récupérer les utilisateurs avec leurs points
      const participants = await prisma.sessionParticipant.findMany({
        include: {
          user: true
        },
        orderBy: {
          points: 'desc'
        },
        take: 50 // Top 50
      });

      // Grouper par utilisateur et calculer les totaux
      const userStats = participants.reduce((acc, participant) => {
        const userId = participant.user.id;
        if (!acc[userId]) {
          acc[userId] = {
            userId,
            name: participant.user.displayName,
            points: 0,
            badges: [], // À implémenter
            rank: 0
          };
        }
        acc[userId].points += participant.points;
        return acc;
      }, {} as Record<string, any>);

      // Convertir en array et trier
      const leaderboard = Object.values(userStats)
        .sort((a: any, b: any) => b.points - a.points)
        .map((user: any, index) => ({
          ...user,
          rank: index + 1
        }));

      res.json(leaderboard);
    } catch (error) {
      console.error('Error getting leaderboard:', error);
      res.status(500).json({ error: 'Failed to get leaderboard' });
    }
  }
}
