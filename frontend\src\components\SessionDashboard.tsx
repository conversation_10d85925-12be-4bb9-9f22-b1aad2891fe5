import { useState } from 'react';
import { Bar<PERSON><PERSON>3, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Music, Star, Trophy } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SessionDashboardProps {
  session: {
    id: string;
    name: string;
    participants: Array<{
      id: string;
      name: string;
      points: number;
      user: { displayName: string };
    }>;
    tracks: Array<{
      id: string;
      title: string;
      artist: string;
      score: number;
      hasPlayed: boolean;
      isPlaying: boolean;
      submitter: { user: { displayName: string } };
    }>;
  };
}

export function SessionDashboard({ session }: SessionDashboardProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'tracks' | 'participants'>('overview');

  // Calculs des statistiques
  const totalTracks = session.tracks.length;
  const playedTracks = session.tracks.filter(t => t.hasPlayed).length;
  const totalVotes = session.tracks.reduce((sum, track) => sum + Math.abs(track.score), 0);
  const averageScore = totalTracks > 0 ? (totalVotes / totalTracks).toFixed(1) : '0';

  // Top contributeurs
  const contributorStats = session.tracks.reduce((acc, track) => {
    const contributor = track.submitter.user.displayName;
    if (!acc[contributor]) {
      acc[contributor] = { tracks: 0, totalScore: 0, avgScore: 0 };
    }
    acc[contributor].tracks++;
    acc[contributor].totalScore += track.score;
    acc[contributor].avgScore = acc[contributor].totalScore / acc[contributor].tracks;
    return acc;
  }, {} as Record<string, { tracks: number; totalScore: number; avgScore: number }>);

  const topContributors = Object.entries(contributorStats)
    .sort(([, a], [, b]) => b.avgScore - a.avgScore)
    .slice(0, 5);

  // Pistes les mieux notées
  const topTracks = session.tracks
    .filter(track => !track.hasPlayed)
    .sort((a, b) => b.score - a.score)
    .slice(0, 5);

  // Progression de la session
  const progressPercentage = totalTracks > 0 ? (playedTracks / totalTracks) * 100 : 0;

  const tabs = [
    { id: 'overview', label: 'Vue d\'ensemble', icon: BarChart3 },
    { id: 'tracks', label: 'Pistes', icon: Music },
    { id: 'participants', label: 'Participants', icon: Users },
  ];

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold">Tableau de bord</h2>
        <div className="flex space-x-1 bg-gray-700 rounded-lg p-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <Button
                key={tab.id}
                size="sm"
                variant={activeTab === tab.id ? "default" : "ghost"}
                onClick={() => setActiveTab(tab.id as any)}
                className="flex items-center space-x-2"
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </Button>
            );
          })}
        </div>
      </div>

      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Métriques principales */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 text-center">
              <Music className="h-6 w-6 mx-auto mb-2 text-blue-400" />
              <p className="text-2xl font-bold">{totalTracks}</p>
              <p className="text-sm text-gray-400">Pistes totales</p>
            </div>
            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 text-center">
              <Star className="h-6 w-6 mx-auto mb-2 text-green-400" />
              <p className="text-2xl font-bold">{playedTracks}</p>
              <p className="text-sm text-gray-400">Pistes jouées</p>
            </div>
            <div className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-4 text-center">
              <TrendingUp className="h-6 w-6 mx-auto mb-2 text-purple-400" />
              <p className="text-2xl font-bold">{totalVotes}</p>
              <p className="text-sm text-gray-400">Votes totaux</p>
            </div>
            <div className="bg-orange-900/20 border border-orange-500/30 rounded-lg p-4 text-center">
              <Trophy className="h-6 w-6 mx-auto mb-2 text-orange-400" />
              <p className="text-2xl font-bold">{averageScore}</p>
              <p className="text-sm text-gray-400">Score moyen</p>
            </div>
          </div>

          {/* Progression */}
          <div className="bg-gray-700/50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold">Progression de la session</h3>
              <span className="text-sm text-gray-400">{Math.round(progressPercentage)}%</span>
            </div>
            <div className="w-full bg-gray-600 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-400 mt-2">
              {playedTracks} sur {totalTracks} pistes jouées
            </p>
          </div>

          {/* Top contributeurs */}
          <div className="bg-gray-700/50 rounded-lg p-4">
            <h3 className="font-semibold mb-3">Top contributeurs</h3>
            <div className="space-y-2">
              {topContributors.map(([name, stats], index) => (
                <div key={name} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className={`text-lg font-bold ${
                      index === 0 ? 'text-yellow-400' : 
                      index === 1 ? 'text-gray-400' : 
                      'text-orange-400'
                    }`}>
                      {index + 1}
                    </span>
                    <span>{name}</span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-semibold">{stats.avgScore.toFixed(1)} pts/piste</p>
                    <p className="text-xs text-gray-400">{stats.tracks} pistes</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'tracks' && (
        <div className="space-y-4">
          <h3 className="font-semibold">Pistes les mieux notées</h3>
          <div className="space-y-2">
            {topTracks.map((track, index) => (
              <div key={track.id} className="flex items-center space-x-3 p-3 bg-gray-700/30 rounded-lg">
                <span className="text-lg font-bold text-gray-400">{index + 1}</span>
                <div className="flex-1">
                  <p className="font-medium">{track.title}</p>
                  <p className="text-sm text-gray-400">{track.artist}</p>
                  <p className="text-xs text-gray-500">par {track.submitter.user.displayName}</p>
                </div>
                <div className="text-right">
                  <span className="text-lg font-bold text-green-400">+{track.score}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'participants' && (
        <div className="space-y-4">
          <h3 className="font-semibold">Classement des participants</h3>
          <div className="space-y-2">
            {session.participants
              .sort((a, b) => b.points - a.points)
              .map((participant, index) => (
                <div key={participant.id} className="flex items-center space-x-3 p-3 bg-gray-700/30 rounded-lg">
                  <span className={`text-lg font-bold ${
                    index === 0 ? 'text-yellow-400' : 
                    index === 1 ? 'text-gray-400' : 
                    index === 2 ? 'text-orange-400' : 'text-gray-500'
                  }`}>
                    {index + 1}
                  </span>
                  <div className="flex-1">
                    <p className="font-medium">{participant.user.displayName}</p>
                  </div>
                  <div className="text-right">
                    <span className="text-lg font-bold">{participant.points} pts</span>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}
