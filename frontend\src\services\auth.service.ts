import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface User {
  id: string;
  spotifyId: string;
  email: string;
  displayName: string;
  avatarUrl: string | null;
  points: number;
  level: number;
  badges: Array<{
    badge: {
      id: string;
      name: string;
      description: string;
      icon: string;
      points: number;
    };
  }>;
}

export class AuthService {
  private static TOKEN_KEY = 'playhifyy_token';

  static async initiateSpotifyLogin(): Promise<{ authUrl: string }> {
    const response = await axios.get(`${API_URL}/auth/spotify/login`);
    return response.data;
  }

  static async handleCallback(token: string): Promise<void> {
    localStorage.setItem(this.TOKEN_KEY, token);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  static async getCurrentUser(): Promise<User> {
    const token = this.getToken();
    if (!token) {
      throw new Error('No token found');
    }

    const response = await axios.get(`${API_URL}/auth/me`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }

  static async getSpotifyAccessToken(): Promise<string> {
    const token = this.getToken();
    if (!token) {
      throw new Error('No token found');
    }

    const response = await axios.get(`${API_URL}/auth/spotify/token`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data.accessToken;
  }

  static async refreshToken(): Promise<void> {
    const token = this.getToken();
    if (!token) {
      throw new Error('No token found');
    }

    const response = await axios.post(
      `${API_URL}/auth/refresh-token`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.data.success) {
      // Token is refreshed server-side, no need to update locally
    }
  }

  static async logout(): Promise<void> {
    const token = this.getToken();
    if (token) {
      try {
        await axios.post(
          `${API_URL}/auth/logout`,
          {},
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
      } catch (error) {
        console.error('Logout error:', error);
      }
    }

    localStorage.removeItem(this.TOKEN_KEY);
    delete axios.defaults.headers.common['Authorization'];
  }

  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  static isAuthenticated(): boolean {
    return !!this.getToken();
  }

  static setupAxiosInterceptors(): void {
    const token = this.getToken();
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }

    // Response interceptor to handle token expiration
    axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && this.getToken()) {
          try {
            await this.refreshToken();
            // Retry the original request
            const originalRequest = error.config;
            originalRequest.headers['Authorization'] = `Bearer ${this.getToken()}`;
            return axios(originalRequest);
          } catch (refreshError) {
            // Refresh failed, redirect to login
            this.logout();
            window.location.href = '/';
          }
        }
        return Promise.reject(error);
      }
    );
  }
}
