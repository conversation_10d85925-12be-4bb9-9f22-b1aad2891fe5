import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { SpotifyService } from '../services/spotify.service';
import { env } from '../config/env';
import { redis } from '../config/redis';

const prisma = new PrismaClient();

export class AuthController {
  /**
   * Initiate Spotify OAuth flow
   */
  static async spotifyLogin(_req: Request, res: Response): Promise<void> {
    try {
      // Generate state for CSRF protection
      const state = crypto.randomBytes(16).toString('hex');
      
      // Store state in Redis with 10 minute expiration
      await redis.setex(`auth:state:${state}`, 600, 'valid');
      
      // Get authorization URL
      const authUrl = SpotifyService.getAuthorizationUrl(state);
      
      res.json({ authUrl });
    } catch (error) {
      console.error('Error initiating Spotify login:', error);
      res.status(500).json({ error: 'Failed to initiate login' });
    }
  }

  /**
   * Handle Spotify OAuth callback
   */
  static async spotifyCallback(req: Request, res: Response): Promise<void> {
    try {
      const { code, state, error } = req.query;

      // Handle OAuth errors
      if (error) {
        return res.redirect(`${env.FRONTEND_URL}/login?error=${error}`);
      }

      // Validate state
      if (!state || typeof state !== 'string') {
        return res.redirect(`${env.FRONTEND_URL}/login?error=invalid_state`);
      }

      const storedState = await redis.get(`auth:state:${state}`);
      if (!storedState) {
        return res.redirect(`${env.FRONTEND_URL}/login?error=invalid_state`);
      }

      // Clean up state
      await redis.del(`auth:state:${state}`);

      // Exchange code for tokens
      if (!code || typeof code !== 'string') {
        return res.redirect(`${env.FRONTEND_URL}/login?error=no_code`);
      }

      const tokenData = await SpotifyService.exchangeCodeForToken(code);
      
      // Get user profile
      const spotifyProfile = await SpotifyService.getUserProfile(tokenData.access_token);
      
      // Find or create user
      let user = await prisma.user.findUnique({
        where: { spotifyId: spotifyProfile.id }
      });

      if (!user) {
        user = await prisma.user.create({
          data: {
            spotifyId: spotifyProfile.id,
            email: spotifyProfile.email,
            displayName: spotifyProfile.display_name || spotifyProfile.id,
            avatarUrl: spotifyProfile.images?.[0]?.url || null,
            refreshToken: tokenData.refresh_token
          }
        });
      } else {
        // Update user info
        user = await prisma.user.update({
          where: { id: user.id },
          data: {
            email: spotifyProfile.email,
            displayName: spotifyProfile.display_name || spotifyProfile.id,
            avatarUrl: spotifyProfile.images?.[0]?.url || null,
            refreshToken: tokenData.refresh_token,
            lastLoginAt: new Date()
          }
        });
      }

      // Cache access token
      await SpotifyService.cacheAccessToken(user.id, tokenData.access_token);

      // Generate JWT
      const jwtToken = jwt.sign(
        { 
          userId: user.id,
          spotifyId: user.spotifyId,
          email: user.email
        },
        env.JWT_SECRET,
        { expiresIn: env.JWT_EXPIRES_IN } as jwt.SignOptions
      );

      // Redirect to frontend with token
      res.redirect(`${env.FRONTEND_URL}/auth/callback?token=${jwtToken}`);
    } catch (error) {
      console.error('Error in Spotify callback:', error);
      res.redirect(`${env.FRONTEND_URL}/login?error=callback_failed`);
    }
  }

  /**
   * Get Spotify access token
   */
  static async getSpotifyToken(req: Request & { user?: any }, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      console.log('Getting Spotify token for user:', userId);

      if (!userId) {
        console.log('No userId found in request');
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Get cached access token
      let accessToken = await SpotifyService.getCachedAccessToken(userId);
      console.log('Cached access token found:', !!accessToken);

      if (!accessToken) {
        console.log('No cached token, attempting to refresh...');
        // Try to refresh token
        const user = await prisma.user.findUnique({
          where: { id: userId }
        });

        if (!user || !user.refreshToken) {
          console.log('No user or refresh token found');
          res.status(401).json({ error: 'No refresh token available' });
          return;
        }

        console.log('Refreshing token for user:', user.email);
        const tokenData = await SpotifyService.refreshAccessToken(user.refreshToken);
        accessToken = tokenData.access_token;

        // Cache new access token
        await SpotifyService.cacheAccessToken(user.id, accessToken);
        console.log('Token refreshed and cached successfully');
      }

      res.json({ accessToken });
    } catch (error) {
      console.error('Error getting Spotify token:', error);
      res.status(500).json({ error: 'Failed to get Spotify token' });
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(req: Request & { user?: any }, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Get user with refresh token
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user || !user.refreshToken) {
        res.status(401).json({ error: 'No refresh token available' });
        return;
      }

      // Refresh the token
      const tokenData = await SpotifyService.refreshAccessToken(user.refreshToken);
      
      // Cache new access token
      await SpotifyService.cacheAccessToken(user.id, tokenData.access_token);

      res.json({ 
        success: true,
        expiresIn: tokenData.expires_in 
      });
    } catch (error) {
      console.error('Error refreshing token:', error);
      res.status(500).json({ error: 'Failed to refresh token' });
    }
  }

  /**
   * Get current user info
   */
  static async getCurrentUser(req: Request & { user?: any }, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          spotifyId: true,
          email: true,
          displayName: true,
          avatarUrl: true,
          points: true,
          level: true,
          createdAt: true,
          badges: {
            include: {
              badge: true
            }
          }
        }
      });

      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      res.json(user);
    } catch (error) {
      console.error('Error getting current user:', error);
      res.status(500).json({ error: 'Failed to get user info' });
    }
  }

  /**
   * Logout
   */
  static async logout(req: Request & { user?: any }, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (userId) {
        // Clear cached access token
        await SpotifyService.clearCachedAccessToken(userId);
      }

      res.json({ success: true });
    } catch (error) {
      console.error('Error during logout:', error);
      res.status(500).json({ error: 'Failed to logout' });
    }
  }
}
