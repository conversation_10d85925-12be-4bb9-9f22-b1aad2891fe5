import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Music, Users, LogIn, LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export default function HomePage() {
  const navigate = useNavigate();
  const { user, isAuthenticated, login, logout, isLoading } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        {/* Header with Auth */}
        <div className="absolute top-4 right-4">
          {isLoading ? (
            <div className="text-white">Chargement...</div>
          ) : isAuthenticated && user ? (
            <div className="flex items-center gap-4">
              <div className="text-white text-right">
                <p className="font-semibold">{user.displayName}</p>
                <p className="text-sm opacity-80">Niveau {user.level} • {user.points} pts</p>
              </div>
              {user.avatarUrl && (
                <img 
                  src={user.avatarUrl} 
                  alt={user.displayName}
                  className="w-10 h-10 rounded-full"
                />
              )}
              <Button
                onClick={logout}
                variant="outline"
                size="sm"
                className="bg-white/10 text-white border-white/20 hover:bg-white/20"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Déconnexion
              </Button>
            </div>
          ) : (
            <Button
              onClick={login}
              className="bg-green-500 hover:bg-green-600 text-white"
            >
              <Music className="h-4 w-4 mr-2" />
              Se connecter avec Spotify
            </Button>
          )}
        </div>

        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <Music className="h-20 w-20 text-white animate-pulse" />
          </div>
          <h1 className="text-5xl font-bold text-white mb-4">
            Playhifyy
          </h1>
          <p className="text-xl text-white/90">
            Créez des playlists collaboratives en temps réel pour vos événements
          </p>
        </div>

        {isAuthenticated ? (
          <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
            <div className="bg-white/10 backdrop-blur-md rounded-lg p-8 text-white">
              <div className="flex justify-center mb-4">
                <Music className="h-12 w-12" />
              </div>
              <h2 className="text-2xl font-semibold mb-4 text-center">
                Créer une session
              </h2>
              <p className="text-center mb-6 text-white/80">
                Devenez l'hôte et contrôlez la musique de votre événement
              </p>
              <Button
                onClick={() => navigate('/create')}
                className="w-full bg-white text-purple-600 hover:bg-white/90"
                size="lg"
              >
                Créer une session
              </Button>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-lg p-8 text-white">
              <div className="flex justify-center mb-4">
                <Users className="h-12 w-12" />
              </div>
              <h2 className="text-2xl font-semibold mb-4 text-center">
                Rejoindre une session
              </h2>
              <p className="text-center mb-6 text-white/80">
                Participez et proposez vos titres favoris
              </p>
              <Button
                onClick={() => navigate('/join')}
                className="w-full bg-white text-blue-600 hover:bg-white/90"
                size="lg"
              >
                Rejoindre une session
              </Button>
            </div>
          </div>
        ) : (
          <div className="max-w-md mx-auto">
            <div className="bg-white/10 backdrop-blur-md rounded-lg p-8 text-white text-center">
              <LogIn className="h-16 w-16 mx-auto mb-4" />
              <h2 className="text-2xl font-semibold mb-4">
                Connexion requise
              </h2>
              <p className="mb-6 text-white/80">
                Connectez-vous avec votre compte Spotify pour créer ou rejoindre des sessions musicales collaboratives
              </p>
              <Button
                onClick={login}
                className="w-full bg-green-500 hover:bg-green-600 text-white"
                size="lg"
              >
                <Music className="h-5 w-5 mr-2" />
                Se connecter avec Spotify
              </Button>
            </div>
          </div>
        )}

        <div className="mt-12 text-center">
          <p className="text-white/70 text-sm">
            Propulsé par Spotify • Créé avec ❤️ pour vos soirées
          </p>
        </div>
      </div>
    </div>
  );
}
