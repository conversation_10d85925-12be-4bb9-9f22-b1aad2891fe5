import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Music, Users, LogIn, LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export default function HomePage() {
  const navigate = useNavigate();
  const { user, isAuthenticated, login, logout, isLoading } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header with Auth */}
        <div className="absolute top-4 right-4">
          {isLoading ? (
            <div className="text-white">Chargement...</div>
          ) : isAuthenticated && user ? (
            <div className="flex items-center gap-4">
              <div className="text-white text-right">
                <p className="font-semibold">{user.displayName}</p>
                <p className="text-sm opacity-80">Niveau {user.level} • {user.points} pts</p>
              </div>
              {user.avatarUrl && (
                <img
                  src={user.avatarUrl}
                  alt={user.displayName}
                  className="w-10 h-10 rounded-full"
                />
              )}
              <Button
                onClick={logout}
                variant="outline"
                size="sm"
                className="bg-white/10 text-white border-white/20 hover:bg-white/20"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Déconnexion
              </Button>
            </div>
          ) : null}
        </div>

        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <Music className="h-16 w-16 text-purple-400 mr-4" />
            <h1 className="text-6xl font-bold text-white">Playhifyy</h1>
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Créez des playlists collaboratives et écoutez de la musique ensemble en temps réel
          </p>
        </div>

        {/* Main Actions */}
        <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {/* Host Section */}
          <div className="bg-gradient-to-br from-purple-800/50 to-purple-900/50 border border-purple-600 rounded-lg p-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <Music className="h-8 w-8 text-yellow-400 mr-2" />
                <h2 className="text-2xl font-bold text-white">Devenir Hôte</h2>
              </div>
              <p className="text-purple-200 mb-6">
                Contrôlez la musique et gérez la session
              </p>
              {!isAuthenticated ? (
                <>
                  <p className="text-sm text-purple-200 mb-4">
                    Connexion Spotify requise pour être hôte
                  </p>
                  <Button
                    onClick={login}
                    className="w-full bg-green-600 hover:bg-green-700 text-white"
                  >
                    <LogIn className="mr-2 h-4 w-4" />
                    Se connecter avec Spotify
                  </Button>
                </>
              ) : (
                <Button
                  onClick={() => navigate('/create')}
                  className="w-full bg-purple-600 hover:bg-purple-700"
                >
                  <Music className="mr-2 h-4 w-4" />
                  Créer une session
                </Button>
              )}
            </div>
          </div>

          {/* Participant with Account */}
          <div className="bg-gradient-to-br from-blue-800/50 to-blue-900/50 border border-blue-600 rounded-lg p-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <Users className="h-8 w-8 text-blue-400 mr-2" />
                <h2 className="text-2xl font-bold text-white">Participant</h2>
              </div>
              <p className="text-blue-200 mb-6">
                Gardez vos points et statistiques
              </p>
              {!isAuthenticated ? (
                <Button
                  onClick={login}
                  className="w-full bg-green-600 hover:bg-green-700 text-white mb-3"
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  Se connecter avec Spotify
                </Button>
              ) : (
                <Button
                  onClick={() => navigate('/join')}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  <Users className="mr-2 h-4 w-4" />
                  Rejoindre une session
                </Button>
              )}
            </div>
          </div>

          {/* Guest Access */}
          <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-600 rounded-lg p-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <Users className="h-8 w-8 text-gray-400 mr-2" />
                <h2 className="text-2xl font-bold text-white">Accès Invité</h2>
              </div>
              <p className="text-gray-300 mb-6">
                Rejoignez rapidement sans compte
              </p>
              <Button
                onClick={() => navigate('/guest')}
                className="w-full bg-gray-600 hover:bg-gray-700"
              >
                <Users className="mr-2 h-4 w-4" />
                Rejoindre en tant qu'invité
              </Button>
              <p className="text-xs text-gray-400 text-center mt-2">
                Vos points ne seront pas sauvegardés
              </p>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-6 max-w-6xl mx-auto mt-16">
          <div className="text-center">
            <Music className="h-12 w-12 text-purple-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">Playlists collaboratives</h3>
            <p className="text-gray-400">
              Ajoutez vos morceaux préférés et votez pour ceux des autres
            </p>
          </div>
          <div className="text-center">
            <Users className="h-12 w-12 text-blue-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">Écoute en groupe</h3>
            <p className="text-gray-400">
              Synchronisez votre écoute avec vos amis en temps réel
            </p>
          </div>
          <div className="text-center">
            <Music className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">Système de points</h3>
            <p className="text-gray-400">
              Gagnez des points en participant et en ajoutant de bons morceaux
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
