import { useState, useEffect } from 'react';
import { Play, Pause, SkipForward, SkipB<PERSON>, Volume2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { AuthService } from '@/services/auth.service';

declare global {
  interface Window {
    onSpotifyWebPlaybackSDKReady: () => void;
    Spotify: any;
  }
}

interface SpotifyPlayerProps {
  token: string;
  trackUri?: string;
  onPlayerReady?: (deviceId: string) => void;
  onStateChange?: (state: any) => void;
  onTokenRefresh?: (newToken: string) => void;
  onNext?: () => void;
  onPrevious?: () => void;
}

export function SpotifyPlayer({ token, trackUri, onPlayerReady, onStateChange, onTokenRefresh, onNext, onPrevious }: SpotifyPlayerProps) {
  const [player, setPlayer] = useState<any>(null);
  const [deviceId, setDeviceId] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTrack, setCurrentTrack] = useState<any>(null);
  const [volume, setVolume] = useState(50);
  const { toast } = useToast();

  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://sdk.scdn.co/spotify-player.js';
    script.async = true;
    document.body.appendChild(script);

    window.onSpotifyWebPlaybackSDKReady = () => {
      const spotifyPlayer = new window.Spotify.Player({
        name: 'Playhifyy Web Player',
        getOAuthToken: async (cb: (token: string) => void) => {
          try {
            // Try to get a fresh Spotify access token
            const freshToken = await AuthService.getSpotifyAccessToken();
            cb(freshToken);

            // Notify parent component of token refresh if callback provided
            if (onTokenRefresh && freshToken !== token) {
              onTokenRefresh(freshToken);
            }
          } catch (error) {
            console.error('Failed to refresh Spotify token:', error);
            // Fallback to the provided token
            cb(token);
          }
        },
        volume: 0.5,
      });

      // Error handling
      spotifyPlayer.addListener('initialization_error', ({ message }: any) => {
        console.error('Failed to initialize', message);
        toast({
          title: 'Erreur Spotify',
          description: 'Impossible d\'initialiser le lecteur',
          variant: 'destructive',
        });
      });

      spotifyPlayer.addListener('authentication_error', async ({ message }: any) => {
        console.error('Failed to authenticate', message);

        try {
          // Try to refresh the token
          await AuthService.refreshToken();

          // Try to get a fresh token and reconnect
          const freshToken = await AuthService.getSpotifyAccessToken();
          if (onTokenRefresh) {
            onTokenRefresh(freshToken);
          }

          toast({
            title: 'Token rafraîchi',
            description: 'Reconnexion en cours...',
          });
        } catch (error) {
          console.error('Failed to refresh token:', error);
          toast({
            title: 'Erreur d\'authentification',
            description: 'Veuillez vous reconnecter à Spotify',
            variant: 'destructive',
          });
        }
      });

      spotifyPlayer.addListener('account_error', ({ message }: any) => {
        console.error('Failed to validate Spotify account', message);
        toast({
          title: 'Erreur de compte',
          description: 'Un compte Spotify Premium est requis',
          variant: 'destructive',
        });
      });

      spotifyPlayer.addListener('playback_error', ({ message }: any) => {
        console.error('Failed to perform playback', message);
      });

      // Playback status updates
      spotifyPlayer.addListener('player_state_changed', (state: any) => {
        if (!state) return;

        setCurrentTrack(state.track_window.current_track);
        setIsPlaying(!state.paused);
        
        if (onStateChange) {
          onStateChange(state);
        }
      });

      // Ready
      spotifyPlayer.addListener('ready', ({ device_id }: any) => {
        console.log('Ready with Device ID', device_id);
        setDeviceId(device_id);
        
        if (onPlayerReady) {
          onPlayerReady(device_id);
        }
      });

      // Not Ready
      spotifyPlayer.addListener('not_ready', ({ device_id }: any) => {
        console.log('Device ID has gone offline', device_id);
      });

      // Connect to the player!
      spotifyPlayer.connect().then((success: boolean) => {
        if (success) {
          console.log('Successfully connected to Spotify!');
        }
      });

      setPlayer(spotifyPlayer);
    };

    return () => {
      if (player) {
        player.disconnect();
      }
    };
  }, [token]);

  useEffect(() => {
    if (player && deviceId && trackUri) {
      // Play specific track
      fetch(`https://api.spotify.com/v1/me/player/play?device_id=${deviceId}`, {
        method: 'PUT',
        body: JSON.stringify({
          uris: [trackUri],
        }),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      }).catch(err => console.error('Error playing track:', err));
    }
  }, [trackUri, deviceId, player, token]);

  const togglePlay = () => {
    if (player) {
      player.togglePlay();
    }
  };

  const nextTrack = () => {
    if (onNext) {
      onNext(); // Utiliser notre logique personnalisée
    } else if (player) {
      player.nextTrack(); // Fallback vers Spotify SDK
    }
  };

  const previousTrack = () => {
    if (onPrevious) {
      onPrevious(); // Utiliser notre logique personnalisée
    } else if (player) {
      player.previousTrack(); // Fallback vers Spotify SDK
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseInt(e.target.value);
    setVolume(newVolume);
    if (player) {
      player.setVolume(newVolume / 100);
    }
  };

  if (!currentTrack) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 text-center">
        <p className="text-gray-400">En attente de connexion au lecteur Spotify...</p>
        <p className="text-sm text-gray-500 mt-2">Assurez-vous d'avoir Spotify Premium</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex items-center space-x-4 mb-6">
        <img
          src={currentTrack.album.images?.[0]?.url || '/placeholder-album.png'}
          alt={currentTrack.name}
          className="w-20 h-20 rounded"
        />
        <div className="flex-1">
          <h3 className="font-semibold text-lg">{currentTrack.name}</h3>
          <p className="text-gray-400">
            {currentTrack.artists.map((artist: any) => artist.name).join(', ')}
          </p>
          <p className="text-sm text-gray-500">{currentTrack.album.name}</p>
        </div>
      </div>

      <div className="flex items-center justify-center space-x-4 mb-4">
        <Button
          size="icon"
          variant="ghost"
          onClick={previousTrack}
        >
          <SkipBack className="h-5 w-5" />
        </Button>
        <Button
          size="icon"
          variant="default"
          onClick={togglePlay}
          className="h-12 w-12"
        >
          {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
        </Button>
        <Button
          size="icon"
          variant="ghost"
          onClick={nextTrack}
        >
          <SkipForward className="h-5 w-5" />
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <Volume2 className="h-4 w-4 text-gray-400" />
        <input
          type="range"
          min="0"
          max="100"
          value={volume}
          onChange={handleVolumeChange}
          className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
        />
        <span className="text-sm text-gray-400 w-10 text-right">{volume}%</span>
      </div>
    </div>
  );
}
