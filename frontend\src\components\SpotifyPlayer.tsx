import { useState, useEffect } from 'react';
import { Play, Pause, Ski<PERSON>Forward, SkipB<PERSON>, Volume2, VolumeX, Heart, ThumbsUp, ThumbsDown, Star, Flame } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { AuthService } from '@/services/auth.service';

const VOTE_EMOJIS = [
  { emoji: '🔥', value: 5, icon: Flame, color: 'text-orange-500', label: 'Excellent' },
  { emoji: '❤️', value: 4, icon: Heart, color: 'text-red-500', label: 'J\'adore' },
  { emoji: '⭐', value: 3, icon: Star, color: 'text-yellow-500', label: 'J\'aime' },
  { emoji: '👍', value: 2, icon: ThumbsUp, color: 'text-blue-500', label: 'Pas mal' },
  { emoji: '👎', value: 1, icon: ThumbsDown, color: 'text-gray-500', label: 'Bof' },
];

declare global {
  interface Window {
    onSpotifyWebPlaybackSDKReady: () => void;
    Spotify: any;
  }
}

interface SpotifyPlayerProps {
  token: string;
  trackUri?: string;
  onPlayerReady?: (deviceId: string) => void;
  onStateChange?: (state: any) => void;
  onTokenRefresh?: (newToken: string) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  currentTrackData?: any;
  onVote?: (emoji: string, value: number) => void;
  userVote?: { emoji: string; value: number } | null;
  votingEnabled?: boolean;
}

export function SpotifyPlayer({
  token,
  trackUri,
  onPlayerReady,
  onStateChange,
  onTokenRefresh,
  onNext,
  onPrevious,
  currentTrackData,
  onVote,
  userVote,
  votingEnabled = false
}: SpotifyPlayerProps) {
  const [player, setPlayer] = useState<any>(null);
  const [deviceId, setDeviceId] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTrack, setCurrentTrack] = useState<any>(null);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  const [previousVolume, setPreviousVolume] = useState(50);
  const [position, setPosition] = useState(0);
  const [duration, setDuration] = useState(0);
  const { toast } = useToast();

  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://sdk.scdn.co/spotify-player.js';
    script.async = true;
    document.body.appendChild(script);

    window.onSpotifyWebPlaybackSDKReady = () => {
      const spotifyPlayer = new window.Spotify.Player({
        name: 'Playhifyy Web Player',
        getOAuthToken: async (cb: (token: string) => void) => {
          try {
            // Try to get a fresh Spotify access token
            const freshToken = await AuthService.getSpotifyAccessToken();
            cb(freshToken);

            // Notify parent component of token refresh if callback provided
            if (onTokenRefresh && freshToken !== token) {
              onTokenRefresh(freshToken);
            }
          } catch (error) {
            console.error('Failed to refresh Spotify token:', error);
            // Fallback to the provided token
            cb(token);
          }
        },
        volume: 0.5,
      });

      // Error handling
      spotifyPlayer.addListener('initialization_error', ({ message }: any) => {
        console.error('Failed to initialize', message);
        toast({
          title: 'Erreur Spotify',
          description: 'Impossible d\'initialiser le lecteur',
          variant: 'destructive',
        });
      });

      spotifyPlayer.addListener('authentication_error', async ({ message }: any) => {
        console.error('Failed to authenticate', message);

        try {
          // Try to refresh the token
          await AuthService.refreshToken();

          // Try to get a fresh token and reconnect
          const freshToken = await AuthService.getSpotifyAccessToken();
          if (onTokenRefresh) {
            onTokenRefresh(freshToken);
          }

          toast({
            title: 'Token rafraîchi',
            description: 'Reconnexion en cours...',
          });
        } catch (error) {
          console.error('Failed to refresh token:', error);
          toast({
            title: 'Erreur d\'authentification',
            description: 'Veuillez vous reconnecter à Spotify',
            variant: 'destructive',
          });
        }
      });

      spotifyPlayer.addListener('account_error', ({ message }: any) => {
        console.error('Failed to validate Spotify account', message);
        toast({
          title: 'Erreur de compte',
          description: 'Un compte Spotify Premium est requis',
          variant: 'destructive',
        });
      });

      spotifyPlayer.addListener('playback_error', ({ message }: any) => {
        console.error('Failed to perform playback', message);
      });

      // Playback status updates
      spotifyPlayer.addListener('player_state_changed', (state: any) => {
        if (!state) return;

        setCurrentTrack(state.track_window.current_track);
        setIsPlaying(!state.paused);
        setPosition(state.position);
        setDuration(state.duration);

        if (onStateChange) {
          onStateChange(state);
        }
      });

      // Ready
      spotifyPlayer.addListener('ready', ({ device_id }: any) => {
        console.log('Ready with Device ID', device_id);
        setDeviceId(device_id);
        
        if (onPlayerReady) {
          onPlayerReady(device_id);
        }
      });

      // Not Ready
      spotifyPlayer.addListener('not_ready', ({ device_id }: any) => {
        console.log('Device ID has gone offline', device_id);
      });

      // Connect to the player!
      spotifyPlayer.connect().then((success: boolean) => {
        if (success) {
          console.log('Successfully connected to Spotify!');
        }
      });

      setPlayer(spotifyPlayer);
    };

    return () => {
      if (player) {
        player.disconnect();
      }
    };
  }, [token]);

  useEffect(() => {
    if (player && deviceId && trackUri) {
      // Play specific track
      fetch(`https://api.spotify.com/v1/me/player/play?device_id=${deviceId}`, {
        method: 'PUT',
        body: JSON.stringify({
          uris: [trackUri],
        }),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      }).catch(err => console.error('Error playing track:', err));
    }
  }, [trackUri, deviceId, player, token]);

  // Update position every second when playing
  useEffect(() => {
    if (!player || !isPlaying) return;

    const interval = setInterval(() => {
      player.getCurrentState().then((state: any) => {
        if (state) {
          setPosition(state.position);
          setDuration(state.duration);
        }
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [player, isPlaying]);

  const togglePlay = () => {
    if (player) {
      player.togglePlay();
    }
  };

  const nextTrack = () => {
    if (onNext) {
      onNext(); // Utiliser notre logique personnalisée
    } else if (player) {
      player.nextTrack(); // Fallback vers Spotify SDK
    }
  };

  const previousTrack = () => {
    if (onPrevious) {
      onPrevious(); // Utiliser notre logique personnalisée
    } else if (player) {
      player.previousTrack(); // Fallback vers Spotify SDK
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseInt(e.target.value);
    setVolume(newVolume);
    if (newVolume > 0) {
      setIsMuted(false);
    }
    if (player) {
      player.setVolume(newVolume / 100);
    }
  };

  const toggleMute = () => {
    if (isMuted) {
      // Unmute: restore previous volume
      setVolume(previousVolume);
      setIsMuted(false);
      if (player) {
        player.setVolume(previousVolume / 100);
      }
    } else {
      // Mute: save current volume and set to 0
      setPreviousVolume(volume);
      setVolume(0);
      setIsMuted(true);
      if (player) {
        player.setVolume(0);
      }
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!player || !duration) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newPosition = percentage * duration;

    player.seek(newPosition).then(() => {
      setPosition(newPosition);
    });
  };

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!currentTrack) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 text-center">
        <p className="text-gray-400">En attente de connexion au lecteur Spotify...</p>
        <p className="text-sm text-gray-500 mt-2">Assurez-vous d'avoir Spotify Premium</p>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 shadow-xl border border-gray-700">
      {/* En-tête du lecteur */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-bold text-white">🎵 Lecteur Spotify</h2>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-xs text-green-400">En ligne</span>
        </div>
      </div>

      {/* Informations de la piste */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="relative">
          <img
            src={currentTrack.album.images?.[0]?.url || '/placeholder-album.png'}
            alt={currentTrack.name}
            className="w-24 h-24 rounded-lg shadow-lg"
          />
          <div className="absolute inset-0 bg-black bg-opacity-20 rounded-lg"></div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-bold text-xl text-white truncate">{currentTrack.name}</h3>
          <p className="text-gray-300 text-lg truncate">
            {currentTrack.artists.map((artist: any) => artist.name).join(', ')}
          </p>
          <p className="text-sm text-gray-400 truncate">{currentTrack.album.name}</p>
        </div>
      </div>

      {/* Barre de progression */}
      <div className="mb-6">
        <div className="flex items-center justify-between text-sm text-gray-300 mb-3">
          <span className="font-mono">{formatTime(position)}</span>
          <span className="font-mono">{formatTime(duration)}</span>
        </div>
        <div
          className="relative w-full bg-gray-700 rounded-full h-3 shadow-inner cursor-pointer"
          onClick={handleProgressClick}
        >
          <div
            className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500 shadow-lg"
            style={{ width: `${duration > 0 ? (position / duration) * 100 : 0}%` }}
          />
          <div
            className="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg border-2 border-purple-500 transition-all duration-500 hover:scale-110"
            style={{ left: `${duration > 0 ? (position / duration) * 100 : 0}%`, marginLeft: '-8px' }}
          />
        </div>
      </div>

      {/* Contrôles de lecture */}
      <div className="flex items-center justify-center space-x-6 mb-6">
        <Button
          size="icon"
          variant="ghost"
          onClick={previousTrack}
          className="h-12 w-12 hover:bg-gray-700 transition-colors"
        >
          <SkipBack className="h-6 w-6 text-gray-300" />
        </Button>
        <Button
          size="icon"
          variant="default"
          onClick={togglePlay}
          className="h-16 w-16 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg transition-all duration-200 transform hover:scale-105"
        >
          {isPlaying ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8 ml-1" />}
        </Button>
        <Button
          size="icon"
          variant="ghost"
          onClick={nextTrack}
          className="h-12 w-12 hover:bg-gray-700 transition-colors"
        >
          <SkipForward className="h-6 w-6 text-gray-300" />
        </Button>
      </div>

      {/* Contrôle de volume */}
      <div className="flex items-center space-x-3 mb-6">
        <Button
          size="sm"
          variant="ghost"
          onClick={toggleMute}
          className="p-2 hover:bg-gray-700"
        >
          {isMuted || volume === 0 ? (
            <VolumeX className="h-5 w-5 text-gray-400" />
          ) : (
            <Volume2 className="h-5 w-5 text-gray-300" />
          )}
        </Button>
        <div className="flex-1 relative">
          <input
            type="range"
            min="0"
            max="100"
            value={volume}
            onChange={handleVolumeChange}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            style={{
              background: `linear-gradient(to right, #8b5cf6 0%, #8b5cf6 ${volume}%, #374151 ${volume}%, #374151 100%)`
            }}
          />
        </div>
        <span className="text-sm text-gray-300 w-12 text-right font-mono">{volume}%</span>
      </div>

      {/* Contrôles de vote */}
      {votingEnabled && onVote && currentTrackData && (
        <div className="border-t border-gray-600 pt-6">
          <h4 className="text-lg font-bold mb-4 text-center text-white">⭐ Votez pour cette piste</h4>
          <div className="flex items-center justify-center space-x-3">
            {VOTE_EMOJIS.map(({ emoji, value, icon: Icon, color, label }) => {
              const isUserVote = userVote?.emoji === emoji;

              return (
                <Button
                  key={emoji}
                  size="lg"
                  variant={isUserVote ? "default" : "ghost"}
                  onClick={() => onVote(emoji, value)}
                  className={`${color} hover:${color} ${isUserVote ? 'ring-2 ring-white bg-gray-700' : 'hover:bg-gray-700'} transition-all duration-200 transform hover:scale-110`}
                  title={isUserVote ? `Votre vote: ${label} (+${value} pts)` : `${label}: +${value} points`}
                >
                  <Icon className="h-5 w-5" />
                </Button>
              );
            })}
          </div>
          {currentTrackData.score !== undefined && (
            <div className="text-center mt-4">
              <div className="inline-flex items-center space-x-2 bg-gray-700 px-4 py-2 rounded-full">
                <span className="text-sm text-gray-300">Score total:</span>
                <span className="text-xl font-bold text-purple-400">{currentTrackData.score}</span>
                <span className="text-sm text-gray-300">pts</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
