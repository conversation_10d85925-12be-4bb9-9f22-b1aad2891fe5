import { Router } from 'express';
import { BadgeController } from '../controllers/badge.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = Router();

// Routes pour les badges et statistiques
router.get('/stats', authMiddleware, BadgeController.getUserStats);
router.get('/stats/:userId', authMiddleware, BadgeController.getUserStats);
router.post('/badges/check', authMiddleware, BadgeController.checkBadges);
router.get('/leaderboard', BadgeController.getLeaderboard);

export default router;
