import { Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { generateSessionCode } from '../utils/auth';
import { redis } from '../config/redis';
import { AuthRequest } from '../middleware/auth.middleware';

const prisma = new PrismaClient();

export class SessionController {
  /**
   * Create a new session
   */
  static async createSession(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { name, settings } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      if (!name || typeof name !== 'string') {
        res.status(400).json({ error: 'Session name is required' });
        return;
      }

      // Generate unique session code
      let code: string;
      let attempts = 0;
      do {
        code = generateSessionCode();
        const existing = await prisma.session.findUnique({ where: { code } });
        if (!existing) break;
        attempts++;
      } while (attempts < 10);

      if (attempts >= 10) {
        res.status(500).json({ error: 'Failed to generate unique session code' });
        return;
      }

      // Create session
      const session = await prisma.session.create({
        data: {
          code,
          name,
          hostId: userId,
          settings: settings || undefined,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        },
        include: {
          host: {
            select: {
              id: true,
              displayName: true,
              avatarUrl: true,
            },
          },
        },
      });

      // Create participant entry for host
      await prisma.sessionParticipant.create({
        data: {
          sessionId: session.id,
          userId,
          name: session.host.displayName,
          role: 'HOST',
        },
      });

      // Cache session data in Redis
      await redis.setex(
        `session:${session.code}`,
        86400, // 24 hours
        JSON.stringify({
          id: session.id,
          hostId: session.hostId,
          isActive: session.isActive,
        })
      );

      // Track game event
      await prisma.gameEvent.create({
        data: {
          userId,
          sessionId: session.id,
          type: 'SESSION_CREATED',
          points: 10,
        },
      });

      res.status(201).json(session);
    } catch (error) {
      console.error('Error creating session:', error);
      res.status(500).json({ error: 'Failed to create session' });
    }
  }

  /**
   * Join a session as guest (no authentication required)
   */
  static async joinSessionAsGuest(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { code, guestName } = req.body;

      if (!code || typeof code !== 'string') {
        res.status(400).json({ error: 'Session code is required' });
        return;
      }

      if (!guestName || typeof guestName !== 'string') {
        res.status(400).json({ error: 'Guest name is required' });
        return;
      }

      // Find session
      const session = await prisma.session.findUnique({
        where: { code: code.toUpperCase() },
      });

      if (!session) {
        res.status(404).json({ error: 'Session not found' });
        return;
      }

      if (!session.isActive) {
        res.status(400).json({ error: 'Session is no longer active' });
        return;
      }

      if (new Date() > session.expiresAt) {
        res.status(400).json({ error: 'Session has expired' });
        return;
      }

      // Generate a temporary guest ID
      const guestId = `guest_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Create guest participant (without userId)
      const participant = await prisma.sessionParticipant.create({
        data: {
          sessionId: session.id,
          name: guestName,
          role: 'PARTICIPANT',
          guestId: guestId,
        },
      });

      res.json({
        session,
        participant,
        guestId,
        isGuest: true,
      });
    } catch (error) {
      console.error('Error joining session as guest:', error);
      res.status(500).json({ error: 'Failed to join session' });
    }
  }

  /**
   * Join a session
   */
  static async joinSession(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { code, displayName } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      if (!code || typeof code !== 'string') {
        res.status(400).json({ error: 'Session code is required' });
        return;
      }

      if (!displayName || typeof displayName !== 'string') {
        res.status(400).json({ error: 'Display name is required' });
        return;
      }

      // Find session
      const session = await prisma.session.findUnique({
        where: { code: code.toUpperCase() },
        include: {
          participants: {
            where: { userId },
          },
        },
      });

      if (!session) {
        res.status(404).json({ error: 'Session not found' });
        return;
      }

      if (!session.isActive) {
        res.status(400).json({ error: 'Session is no longer active' });
        return;
      }

      if (new Date() > session.expiresAt) {
        res.status(400).json({ error: 'Session has expired' });
        return;
      }

      // Check if already joined
      if (session.participants.length > 0) {
        res.json({
          session,
          participant: session.participants[0],
        });
        return;
      }

      // Create participant
      const participant = await prisma.sessionParticipant.create({
        data: {
          sessionId: session.id,
          userId,
          name: displayName,
          role: 'PARTICIPANT',
        },
      });

      // Track game event
      await prisma.gameEvent.create({
        data: {
          userId,
          sessionId: session.id,
          type: 'SESSION_JOINED',
          points: 5,
        },
      });

      res.json({
        session,
        participant,
      });
    } catch (error) {
      console.error('Error joining session:', error);
      res.status(500).json({ error: 'Failed to join session' });
    }
  }

  /**
   * Get session details for guest
   */
  static async getSessionAsGuest(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { code } = req.params;
      const { guestId } = req.query;

      if (!guestId || typeof guestId !== 'string') {
        res.status(400).json({ error: 'Guest ID is required' });
        return;
      }

      // Find session
      const session = await prisma.session.findUnique({
        where: { code: code.toUpperCase() },
        include: {
          host: {
            select: {
              id: true,
              displayName: true,
              avatarUrl: true,
            },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  displayName: true,
                  avatarUrl: true,
                },
              },
            },
          },
          tracks: {
            orderBy: [
              { score: 'desc' },
              { addedAt: 'asc' },
            ],
            include: {
              submitter: {
                include: {
                  user: {
                    select: {
                      displayName: true,
                      avatarUrl: true,
                    },
                  },
                },
              },
              votes: true,
            },
          },
        },
      });

      if (!session) {
        res.status(404).json({ error: 'Session not found' });
        return;
      }

      // Check if guest is participant
      const participant = await prisma.sessionParticipant.findFirst({
        where: {
          sessionId: session.id,
          guestId: guestId,
        },
      });

      if (!participant) {
        res.status(403).json({ error: 'Not a participant of this session' });
        return;
      }

      res.json({
        ...session,
        currentParticipant: participant,
        isGuest: true,
      });
    } catch (error) {
      console.error('Error getting session as guest:', error);
      res.status(500).json({ error: 'Failed to get session' });
    }
  }

  /**
   * Get session details
   */
  static async getSession(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { code } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Try to get from cache first
      const cached = await redis.get(`session:${code}`);
      let session;

      if (cached) {
        const cachedData = JSON.parse(cached);
        session = await prisma.session.findUnique({
          where: { id: cachedData.id },
          include: {
            host: {
              select: {
                id: true,
                displayName: true,
                avatarUrl: true,
              },
            },
            participants: {
              include: {
                user: {
                  select: {
                    id: true,
                    displayName: true,
                    avatarUrl: true,
                  },
                },
              },
            },
            tracks: {
              orderBy: [
                { score: 'desc' },
                { addedAt: 'asc' },
              ],
              include: {
                submitter: {
                  include: {
                    user: {
                      select: {
                        displayName: true,
                        avatarUrl: true,
                      },
                    },
                  },
                },
                votes: {
                  where: {
                    user: {
                      userId,
                    },
                  },
                },
              },
            },
          },
        });
      } else {
        session = await prisma.session.findUnique({
          where: { code: code.toUpperCase() },
          include: {
            host: {
              select: {
                id: true,
                displayName: true,
                avatarUrl: true,
              },
            },
            participants: {
              include: {
                user: {
                  select: {
                    id: true,
                    displayName: true,
                    avatarUrl: true,
                  },
                },
              },
            },
            tracks: {
              orderBy: [
                { score: 'desc' },
                { addedAt: 'asc' },
              ],
              include: {
                submitter: {
                  include: {
                    user: {
                      select: {
                        displayName: true,
                        avatarUrl: true,
                      },
                    },
                  },
                },
                votes: {
                  where: {
                    user: {
                      userId,
                    },
                  },
                },
              },
            },
          },
        });

        // Cache if found
        if (session) {
          await redis.setex(
            `session:${session.code}`,
            86400,
            JSON.stringify({
              id: session.id,
              hostId: session.hostId,
              isActive: session.isActive,
            })
          );
        }
      }

      if (!session) {
        res.status(404).json({ error: 'Session not found' });
        return;
      }

      // Check if user is participant
      const participant = await prisma.sessionParticipant.findUnique({
        where: {
          sessionId_userId: {
            sessionId: session.id,
            userId,
          },
        },
      });

      if (!participant) {
        res.status(403).json({ error: 'Not a participant of this session' });
        return;
      }

      res.json({
        ...session,
        currentParticipant: participant,
      });
    } catch (error) {
      console.error('Error getting session:', error);
      res.status(500).json({ error: 'Failed to get session' });
    }
  }

  /**
   * Update session settings (host only)
   */
  static async updateSession(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { code } = req.params;
      const { settings } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const session = await prisma.session.findUnique({
        where: { code: code.toUpperCase() },
      });

      if (!session) {
        res.status(404).json({ error: 'Session not found' });
        return;
      }

      if (session.hostId !== userId) {
        res.status(403).json({ error: 'Only the host can update session settings' });
        return;
      }

      const updated = await prisma.session.update({
        where: { id: session.id },
        data: { settings },
      });

      // Update cache
      await redis.setex(
        `session:${session.code}`,
        86400,
        JSON.stringify({
          id: updated.id,
          hostId: updated.hostId,
          isActive: updated.isActive,
        })
      );

      res.json(updated);
    } catch (error) {
      console.error('Error updating session:', error);
      res.status(500).json({ error: 'Failed to update session' });
    }
  }

  /**
   * End session (host only)
   */
  static async endSession(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { code } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const session = await prisma.session.findUnique({
        where: { code: code.toUpperCase() },
      });

      if (!session) {
        res.status(404).json({ error: 'Session not found' });
        return;
      }

      if (session.hostId !== userId) {
        res.status(403).json({ error: 'Only the host can end the session' });
        return;
      }

      await prisma.session.update({
        where: { id: session.id },
        data: { isActive: false },
      });

      // Remove from cache
      await redis.del(`session:${session.code}`);

      res.json({ success: true });
    } catch (error) {
      console.error('Error ending session:', error);
      res.status(500).json({ error: 'Failed to end session' });
    }
  }
}
