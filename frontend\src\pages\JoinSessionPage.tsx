import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Users } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { sessionService } from '@/services/api.service';

export default function JoinSessionPage() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, login } = useAuth();
  const [isJoining, setIsJoining] = useState(false);
  const [sessionCode, setSessionCode] = useState('');
  const [displayName, setDisplayName] = useState(user?.displayName || '');

  const handleJoinSession = async () => {
    if (!sessionCode.trim()) {
      toast({
        title: "Erreur",
        description: "Veuillez entrer un code de session",
        variant: "destructive",
      });
      return;
    }

    if (!user) {
      toast({
        title: "Erreur",
        description: "Vous devez être connecté pour rejoindre une session",
        variant: "destructive",
      });
      return;
    }

    if (!displayName.trim()) {
      toast({
        title: "Erreur",
        description: "Veuillez entrer votre nom",
        variant: "destructive",
      });
      return;
    }

    setIsJoining(true);
    
    try {
      const result = await sessionService.join(sessionCode.toUpperCase(), displayName);
      
      toast({
        title: "Session rejointe !",
        description: `Bienvenue dans ${result.session.name}`,
      });
      
      navigate(`/session/${result.session.code}`);
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.response?.data?.error || "Impossible de rejoindre la session",
        variant: "destructive",
      });
    } finally {
      setIsJoining(false);
    }
  };

  const handleSpotifyLogin = () => {
    login();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <Button
          variant="ghost"
          className="mb-6 text-white hover:text-white/80"
          onClick={() => navigate('/')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>

        <div className="bg-white/10 backdrop-blur-md rounded-lg p-8">
          <div className="flex justify-center mb-6">
            <Users className="h-16 w-16 text-white" />
          </div>
          
          <h1 className="text-3xl font-bold text-white text-center mb-8">
            Rejoindre une session
          </h1>

          <div className="space-y-6">
            {!user ? (
              <div className="text-center">
                <p className="text-white mb-4">
                  Connectez-vous avec Spotify pour rejoindre une session
                </p>
                <Button
                  onClick={handleSpotifyLogin}
                  className="w-full bg-green-600 hover:bg-green-700 text-white"
                  size="lg"
                >
                  <Users className="mr-2 h-5 w-5" />
                  Se connecter avec Spotify
                </Button>
              </div>
            ) : (
              <>
                <div className="text-center mb-4">
                  <p className="text-white">
                    Connecté en tant que <strong>{user.displayName}</strong>
                  </p>
                </div>

                <div>
                  <label className="block text-white text-sm font-medium mb-2">
                    Code de session
                  </label>
                  <input
                    type="text"
                    value={sessionCode}
                    onChange={(e) => setSessionCode(e.target.value.toUpperCase())}
                    className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-md text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 text-center text-2xl font-mono"
                    placeholder="ABC123"
                    maxLength={6}
                  />
                </div>

                <div>
                  <label className="block text-white text-sm font-medium mb-2">
                    Votre nom d'affichage
                  </label>
                  <input
                    type="text"
                    value={displayName}
                    onChange={(e) => setDisplayName(e.target.value)}
                    className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-md text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                    placeholder="Jean"
                    onKeyPress={(e) => e.key === 'Enter' && handleJoinSession()}
                  />
                </div>

                <div className="pt-4">
                  <Button
                    onClick={handleJoinSession}
                    disabled={isJoining}
                    className="w-full bg-white text-purple-600 hover:bg-white/90"
                    size="lg"
                  >
                    {isJoining ? "Connexion en cours..." : "Rejoindre la session"}
                  </Button>
                </div>
              </>
            )}
          </div>

          <p className="mt-6 text-center text-white/70 text-sm">
            Demandez le code de session à l'hôte
          </p>
        </div>
      </div>
    </div>
  );
}
