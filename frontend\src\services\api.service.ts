import axios from 'axios';
import { AuthService } from './auth.service';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = AuthService.getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401 && AuthService.getToken()) {
      try {
        await AuthService.refreshToken();
        // Retry the original request
        const originalRequest = error.config;
        originalRequest.headers['Authorization'] = `Bearer ${AuthService.getToken()}`;
        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        AuthService.logout();
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

export const sessionService = {
  create: async (name: string, settings?: any) => {
    const response = await api.post('/sessions', { name, settings });
    return response.data;
  },

  join: async (code: string, displayName: string) => {
    const response = await api.post('/sessions/join', { code, displayName });
    return response.data;
  },

  get: async (code: string) => {
    const response = await api.get(`/sessions/${code}`);
    return response.data;
  },

  update: async (code: string, settings: any) => {
    const response = await api.put(`/sessions/${code}`, { settings });
    return response.data;
  },

  end: async (code: string) => {
    const response = await api.delete(`/sessions/${code}`);
    return response.data;
  },
};

export const trackService = {
  search: async (query: string, limit = 20, offset = 0) => {
    const response = await api.get('/tracks/search', {
      params: { query, limit, offset },
    });
    return response.data;
  },

  add: async (sessionCode: string, spotifyId: string) => {
    const response = await api.post(`/tracks/sessions/${sessionCode}/tracks`, { spotifyId });
    return response.data;
  },

  vote: async (sessionCode: string, trackId: string, emoji: string, value: number) => {
    const response = await api.post(`/tracks/sessions/${sessionCode}/tracks/${trackId}/vote`, {
      emoji,
      value,
    });
    return response.data;
  },

  remove: async (sessionCode: string, trackId: string) => {
    const response = await api.delete(`/tracks/sessions/${sessionCode}/tracks/${trackId}`);
    return response.data;
  },

  updatePlaying: async (sessionCode: string, trackId: string) => {
    const response = await api.put(`/tracks/sessions/${sessionCode}/tracks/${trackId}/playing`);
    return response.data;
  },
};

export const playlistService = {
  export: async (sessionId: string, playlistName: string, playlistDescription?: string, isPublic = true) => {
    const response = await api.post(`/playlists/export/${sessionId}`, {
      playlistName,
      playlistDescription,
      isPublic,
    });
    return response.data;
  },

  getExportable: async () => {
    const response = await api.get('/playlists/exportable');
    return response.data;
  },

  getPreview: async (sessionId: string) => {
    const response = await api.get(`/playlists/export/${sessionId}/preview`);
    return response.data;
  },
};

export const gamificationService = {
  getLeaderboard: async (limit = 10) => {
    const response = await api.get('/playlists/leaderboard', {
      params: { limit },
    });
    return response.data;
  },

  getUserStats: async (userId?: string) => {
    const url = userId ? `/playlists/stats/${userId}` : '/playlists/stats';
    const response = await api.get(url);
    return response.data;
  },

  checkBadges: async () => {
    const response = await api.post('/playlists/badges/check');
    return response.data;
  },
};

export default api;
