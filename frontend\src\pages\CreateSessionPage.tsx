import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Music } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { sessionService } from '@/services/api.service';

export default function CreateSessionPage() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, login } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [sessionName, setSessionName] = useState('');

  const handleCreateSession = async () => {
    if (!sessionName.trim()) {
      toast({
        title: "Erreur",
        description: "Veuillez entrer un nom pour la session",
        variant: "destructive",
      });
      return;
    }

    if (!user) {
      toast({
        title: "Erreur",
        description: "Vous devez être connecté pour créer une session",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    
    try {
      const session = await sessionService.create(sessionName, {
        allowSubmissions: true,
        votingEnabled: true,
        maxTracksPerUser: 10,
      });

      toast({
        title: "Session créée !",
        description: `Code de session : ${session.code}`,
      });
      
      navigate(`/session/${session.code}`);
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.response?.data?.error || "Impossible de créer la session",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleSpotifyLogin = () => {
    login();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <Button
          variant="ghost"
          className="mb-6 text-white hover:text-white/80"
          onClick={() => navigate('/')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>

        <div className="bg-white/10 backdrop-blur-md rounded-lg p-8">
          <div className="flex justify-center mb-6">
            <Music className="h-16 w-16 text-white" />
          </div>
          
          <h1 className="text-3xl font-bold text-white text-center mb-8">
            Créer une session
          </h1>

          <div className="space-y-6">
            {!user ? (
              <div className="text-center">
                <p className="text-white mb-4">
                  Connectez-vous avec Spotify pour créer une session
                </p>
                <Button
                  onClick={handleSpotifyLogin}
                  className="w-full bg-green-600 hover:bg-green-700 text-white"
                  size="lg"
                >
                  <Music className="mr-2 h-5 w-5" />
                  Se connecter avec Spotify
                </Button>
              </div>
            ) : (
              <>
                <div className="text-center mb-4">
                  <p className="text-white">
                    Connecté en tant que <strong>{user.displayName}</strong>
                  </p>
                </div>

                <div>
                  <label className="block text-white text-sm font-medium mb-2">
                    Nom de la session
                  </label>
                  <input
                    type="text"
                    value={sessionName}
                    onChange={(e) => setSessionName(e.target.value)}
                    className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-md text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                    placeholder="Soirée d'anniversaire de Marie"
                    onKeyPress={(e) => e.key === 'Enter' && handleCreateSession()}
                  />
                </div>

                <div className="pt-4">
                  <Button
                    onClick={handleCreateSession}
                    disabled={isCreating}
                    className="w-full bg-white text-purple-600 hover:bg-white/90"
                    size="lg"
                  >
                    {isCreating ? "Création en cours..." : "Créer la session"}
                  </Button>
                </div>
              </>
            )}
          </div>

          <p className="mt-6 text-center text-white/70 text-sm">
            Un compte Spotify Premium est requis pour contrôler la lecture
          </p>
        </div>
      </div>
    </div>
  );
}
