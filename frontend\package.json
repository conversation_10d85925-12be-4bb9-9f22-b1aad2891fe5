{"name": "@playhifyy/frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.28.4", "@types/lodash": "^4.17.17", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lodash": "^4.17.21", "lucide-react": "^0.363.0", "qrcode": "^1.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20.11.30", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.69", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "typescript": "^5.4.3", "vite": "^5.2.3"}}