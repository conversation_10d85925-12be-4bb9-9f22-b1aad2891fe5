import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import { PlaylistExportService } from '../services/playlist-export.service';
import { GamificationService } from '../services/gamification.service';

export class PlaylistController {
  /**
   * Export session to Spotify playlist
   */
  static async exportToSpotify(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const { playlistName, playlistDescription, isPublic } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const result = await PlaylistExportService.exportToSpotify(
        sessionId,
        userId,
        playlistName,
        playlistDescription,
        isPublic !== undefined ? isPublic : true
      );

      res.json({
        success: true,
        ...result,
      });
    } catch (error: any) {
      console.error('Error exporting playlist:', error);
      res.status(500).json({ error: error.message || 'Failed to export playlist' });
    }
  }

  /**
   * Get exportable sessions
   */
  static async getExportableSessions(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const sessions = await PlaylistExportService.getExportableSessions(userId);
      res.json(sessions);
    } catch (error) {
      console.error('Error getting exportable sessions:', error);
      res.status(500).json({ error: 'Failed to get exportable sessions' });
    }
  }

  /**
   * Get export preview
   */
  static async getExportPreview(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const preview = await PlaylistExportService.getExportPreview(sessionId);
      res.json(preview);
    } catch (error: any) {
      console.error('Error getting export preview:', error);
      res.status(500).json({ error: error.message || 'Failed to get export preview' });
    }
  }

  /**
   * Get leaderboard
   */
  static async getLeaderboard(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { limit = 10 } = req.query;
      const leaderboard = await GamificationService.getLeaderboard(Number(limit));
      res.json(leaderboard);
    } catch (error) {
      console.error('Error getting leaderboard:', error);
      res.status(500).json({ error: 'Failed to get leaderboard' });
    }
  }

  /**
   * Get user stats
   */
  static async getUserStats(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.params.userId || req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const stats = await GamificationService.getUserStats(userId);
      
      if (!stats) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      res.json(stats);
    } catch (error) {
      console.error('Error getting user stats:', error);
      res.status(500).json({ error: 'Failed to get user stats' });
    }
  }

  /**
   * Check and award badges
   */
  static async checkBadges(req: AuthRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const awardedBadges = await GamificationService.checkAndAwardBadges(userId);
      await GamificationService.updateUserLevel(userId);

      res.json({
        awardedBadges,
        newBadgesCount: awardedBadges.length,
      });
    } catch (error) {
      console.error('Error checking badges:', error);
      res.status(500).json({ error: 'Failed to check badges' });
    }
  }
}
