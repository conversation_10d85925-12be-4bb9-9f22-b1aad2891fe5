import { Router } from 'express';
import { Session<PERSON>ontroller } from '../controllers/session.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = Router();

// Guest routes (no authentication required)
router.post('/join-guest', SessionController.joinSessionAsGuest);
router.get('/guest/:code', SessionController.getSessionAsGuest);

// All other session routes require authentication
router.use(authMiddleware);

// Session management
router.post('/', SessionController.createSession);
router.post('/join', SessionController.joinSession);
router.get('/:code', SessionController.getSession);
router.put('/:code', SessionController.updateSession);
router.delete('/:code', SessionController.endSession);

export default router;
