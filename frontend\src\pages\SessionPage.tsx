import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Music, Users, Trophy, Search, Plus, X, Loader2, Heart, ThumbsUp, Star, Flame, Zap } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { sessionService, trackService } from '@/services/api.service';
import { AuthService } from '@/services/auth.service';
import { SpotifyPlayer } from '@/components/SpotifyPlayer';
import { SessionStats } from '@/components/SessionStats';
import { LiveNotifications } from '@/components/LiveNotifications';
import { BadgeDisplay } from '@/components/BadgeDisplay';
import io, { Socket } from 'socket.io-client';
import debounce from 'lodash/debounce';

interface Track {
  id: string;
  spotifyId: string;
  title: string;
  artist: string;
  album: string | null;
  duration: number;
  imageUrl: string | null;
  score: number;
  isPlaying: boolean;
  hasPlayed: boolean;
  position: number;
  addedAt?: string | Date;
  submitter: {
    user: {
      displayName: string;
      avatarUrl: string | null;
    };
  };
  votes: Array<{
    participantId: string;
    emoji: string;
    value: number;
  }>;
}

interface Session {
  id: string;
  code: string;
  name: string;
  hostId: string;
  isActive: boolean;
  settings: {
    allowSubmissions: boolean;
    votingEnabled: boolean;
    maxTracksPerUser: number;
  };
  host: {
    id: string;
    displayName: string;
    avatarUrl: string | null;
  };
  participants: Array<{
    id: string;
    name: string;
    points: number;
    user: {
      id: string;
      displayName: string;
      avatarUrl: string | null;
    };
  }>;
  tracks: Track[];
  currentParticipant: {
    id: string;
    role: 'HOST' | 'PARTICIPANT';
  };
}

interface SpotifyTrack {
  id: string;
  name: string;
  artists: Array<{ name: string }>;
  album: {
    name: string;
    images: Array<{ url: string }>;
  };
  duration_ms: number;
  uri: string;
}

const VOTE_EMOJIS = [
  { emoji: '❤️', value: 3, icon: Heart, color: 'text-red-500' },
  { emoji: '👍', value: 2, icon: ThumbsUp, color: 'text-blue-500' },
  { emoji: '⭐', value: 2, icon: Star, color: 'text-yellow-500' },
  { emoji: '🔥', value: 4, icon: Flame, color: 'text-orange-500' },
  { emoji: '⚡', value: 1, icon: Zap, color: 'text-purple-500' },
];

export default function SessionPage() {
  const { sessionId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SpotifyTrack[]>([]);
  const [searching, setSearching] = useState(false);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [spotifyToken, setSpotifyToken] = useState<string>('');
  const [currentTrackUri, setCurrentTrackUri] = useState<string>('');
  const [deviceId, setDeviceId] = useState<string>('');

  // Recherche dynamique avec debounce
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        return;
      }
      
      setSearching(true);
      try {
        const results = await trackService.search(query);
        setSearchResults(results.tracks.items);
      } catch (error) {
        console.error('Search error:', error);
      } finally {
        setSearching(false);
      }
    }, 300),
    []
  );

  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery, debouncedSearch]);

  // Charger la session et configurer WebSocket
  useEffect(() => {
    if (!sessionId) return;

    loadSession();

    // Récupérer le token JWT pour l'authentification
    const jwtToken = AuthService.getToken();
    if (!jwtToken) {
      toast({
        title: "Erreur",
        description: "Vous devez être connecté",
        variant: "destructive",
      });
      navigate('/');
      return;
    }

    // Récupérer le token Spotify pour le lecteur
    const getSpotifyToken = async () => {
      try {
        console.log('Attempting to get Spotify access token...');
        const spotifyAccessToken = await AuthService.getSpotifyAccessToken();
        console.log('Spotify access token retrieved successfully');
        setSpotifyToken(spotifyAccessToken);
      } catch (error) {
        console.error('Failed to get Spotify token:', error);
        toast({
          title: "Erreur Spotify",
          description: "Impossible de récupérer le token Spotify",
          variant: "destructive",
        });
      }
    };

    getSpotifyToken();

    // Connexion WebSocket
    const newSocket = io('http://localhost:3001', {
      auth: { token: jwtToken }
    });
    
    newSocket.on('connect', () => {
      console.log('Connected to WebSocket');
      newSocket.emit('join-session', sessionId);
    });

    newSocket.on('error', (error) => {
      console.error('WebSocket error:', error);
    });

    newSocket.on('session-state', (sessionState) => {
      setSession(sessionState);
      updateCurrentTrack(sessionState);
    });

    newSocket.on('vote-updated', (data) => {
      console.log('Vote updated:', data);
      loadSession(); // Recharger la session pour mettre à jour les scores
    });

    newSocket.on('track-updated', (data) => {
      console.log('Track updated:', data);
      loadSession(); // Recharger la session pour mettre à jour l'état des tracks
    });
    
    newSocket.on('track-update', (update) => {
      if (update.type === 'track_added' || update.type === 'track_voted') {
        loadSession();
      } else if (update.type === 'track_removed') {
        setSession(prev => prev ? {
          ...prev,
          tracks: prev.tracks.filter(t => t.id !== update.data.trackId)
        } : null);
      } else if (update.type === 'track_playing') {
        setSession(prev => {
          if (!prev) return null;
          const updated = {
            ...prev,
            tracks: prev.tracks.map(t => ({
              ...t,
              isPlaying: t.id === update.data.trackId,
              hasPlayed: t.id === update.data.trackId ? true : t.hasPlayed
            }))
          };
          updateCurrentTrack(updated);
          return updated;
        });
      }
    });
    
    newSocket.on('vote-update', () => {
      loadSession();
    });
    
    newSocket.on('participant-joined', ({ name }) => {
      toast({
        title: "Nouveau participant",
        description: `${name} a rejoint la session`,
      });
      loadSession();
    });

    newSocket.on('participant-left', () => {
      loadSession();
    });
    
    setSocket(newSocket);
    
    return () => {
      newSocket.emit('leave-session');
      newSocket.disconnect();
    };
  }, [sessionId, navigate, toast]);

  const loadSession = async () => {
    if (!sessionId) return;
    
    try {
      const data = await sessionService.get(sessionId);
      setSession(data);
      updateCurrentTrack(data);
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.response?.data?.error || "Impossible de charger la session",
        variant: "destructive",
      });
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const updateCurrentTrack = (sessionData: Session) => {
    const playingTrack = sessionData.tracks.find(t => t.isPlaying);
    if (playingTrack) {
      setCurrentTrackUri(`spotify:track:${playingTrack.spotifyId}`);
    }
  };

  const addTrack = async (spotifyTrack: SpotifyTrack) => {
    if (!session) return;
    
    try {
      await trackService.add(session.code, spotifyTrack.id);
      toast({
        title: "Track ajouté !",
        description: `${spotifyTrack.name} a été ajouté à la file d'attente`,
      });
      setSearchQuery('');
      setSearchResults([]);
      
      if (socket) {
        socket.emit('track-update', {
          type: 'track_added',
          data: { spotifyId: spotifyTrack.id },
          sessionId: session.id,
          userId: user?.id
        });
      }
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.response?.data?.error || "Impossible d'ajouter le track",
        variant: "destructive",
      });
    }
  };

  const voteTrack = async (trackId: string, emoji: string, value: number) => {
    if (!session || !session.currentParticipant) return;
    
    // Vérifier si l'utilisateur a déjà voté avec cet emoji
    const track = session.tracks.find(t => t.id === trackId);
    if (track?.votes.some(v => v.participantId === session.currentParticipant.id && v.emoji === emoji)) {
      toast({
        title: "Vote déjà enregistré",
        description: "Vous avez déjà voté avec cet emoji",
        variant: "destructive",
      });
      return;
    }
    
    try {
      await trackService.vote(session.code, trackId, emoji, value);

      // Recharger immédiatement la session pour mettre à jour l'interface
      await loadSession();

      if (socket) {
        socket.emit('vote-update', {
          trackId,
          emoji,
          value,
          sessionId: session.id,
          userId: user?.id
        });
      }

      toast({
        title: "Vote enregistré !",
        description: `Vous avez voté ${emoji} pour cette piste`,
      });
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.response?.data?.error || "Impossible de voter",
        variant: "destructive",
      });
    }
  };

  const removeTrack = async (trackId: string) => {
    if (!session) return;
    
    try {
      await trackService.remove(session.code, trackId);
      toast({
        title: "Track supprimé",
      });
      
      if (socket) {
        socket.emit('track-update', {
          type: 'track_removed',
          data: { trackId },
          sessionId: session.id,
          userId: user?.id
        });
      }
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.response?.data?.error || "Impossible de supprimer le track",
        variant: "destructive",
      });
    }
  };

  const playTrack = async (track: Track) => {
    if (!session || !isHost || !deviceId) return;
    
    try {
      // Get fresh Spotify token if needed
      let currentToken = spotifyToken;
      if (!currentToken) {
        try {
          currentToken = await AuthService.getSpotifyAccessToken();
          setSpotifyToken(currentToken);
        } catch (error) {
          toast({
            title: "Erreur",
            description: "Impossible de récupérer le token Spotify",
            variant: "destructive",
          });
          return;
        }
      }

      // Jouer le track sur le device Spotify
      await fetch(`https://api.spotify.com/v1/me/player/play?device_id=${deviceId}`, {
        method: 'PUT',
        body: JSON.stringify({
          uris: [`spotify:track:${track.spotifyId}`],
        }),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentToken}`,
        },
      });

      // Mettre à jour le statut dans la base de données
      await trackService.updatePlaying(session.code, track.id);
      
      if (socket) {
        socket.emit('track-update', {
          type: 'track_playing',
          data: { trackId: track.id },
          sessionId: session.id,
          userId: user?.id
        });
      }
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: "Impossible de lire le track",
        variant: "destructive",
      });
    }
  };

  const handlePlayerReady = (newDeviceId: string) => {
    setDeviceId(newDeviceId);
    toast({
      title: "Lecteur Spotify prêt",
      description: "Vous pouvez maintenant contrôler la lecture",
    });
  };

  const handlePlayerStateChange = (state: any) => {
    if (!state) return;

    // Si la piste actuelle est terminée, passer à la suivante selon l'ordre des votes
    if (state.paused && state.position === 0 && state.track_window.previous_tracks.length > 0) {
      console.log('Track ended, playing next track based on votes...');
      playNextTrack();
    }
  };

  const handleTokenRefresh = (newToken: string) => {
    setSpotifyToken(newToken);
  };

  // Fonction pour obtenir la prochaine piste selon l'ordre de lecture et les votes
  const getNextTrack = () => {
    if (!session) return null;

    // Filtrer les pistes non jouées
    const unplayedTracks = session.tracks.filter(track => !track.hasPlayed && !track.isPlaying);

    if (unplayedTracks.length === 0) return null;

    // Trier par score (votes) décroissant, puis par ordre d'ajout
    const sortedTracks = unplayedTracks.sort((a, b) => {
      if (b.score !== a.score) {
        return b.score - a.score; // Score décroissant
      }
      // Si même score, trier par ordre d'ajout (addedAt)
      return new Date(a.addedAt || 0).getTime() - new Date(b.addedAt || 0).getTime();
    });

    return sortedTracks[0];
  };

  // Fonction pour obtenir la piste précédente
  const getPreviousTrack = () => {
    if (!session) return null;

    const playedTracks = session.tracks.filter(track => track.hasPlayed);
    if (playedTracks.length === 0) return null;

    // Retourner la dernière piste jouée
    return playedTracks[playedTracks.length - 1];
  };

  // Fonction pour passer à la piste suivante
  const playNextTrack = async () => {
    const nextTrack = getNextTrack();
    if (nextTrack) {
      await playTrack(nextTrack);
    } else {
      toast({
        title: "Fin de la playlist",
        description: "Toutes les pistes ont été jouées",
      });
    }
  };

  // Fonction pour revenir à la piste précédente
  const playPreviousTrack = async () => {
    if (!session) return;

    const previousTrack = getPreviousTrack();
    if (previousTrack) {
      // Marquer la piste comme non jouée pour pouvoir la rejouer
      await trackService.updatePlaying(session.code, previousTrack.id);
      await playTrack(previousTrack);
    } else {
      toast({
        title: "Début de la playlist",
        description: "Aucune piste précédente disponible",
      });
    }
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getUserVoteForTrack = (track: Track, emoji: string) => {
    if (!session?.currentParticipant) return false;
    return track.votes.some(v => 
      v.participantId === session.currentParticipant.id && v.emoji === emoji
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-purple-500" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const isHost = session.currentParticipant?.role === 'HOST';

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Notifications en temps réel */}
      <LiveNotifications socket={socket} />

      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Music className="h-8 w-8 text-purple-500" />
            <div>
              <h1 className="text-xl font-bold">{session.name}</h1>
              <p className="text-sm text-gray-400">Code: {session.code}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>{session.participants.length} participants</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/')}
            >
              Quitter
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-4 grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Playlist principale */}
        <div className="lg:col-span-2 space-y-4">
          {/* Lecteur Spotify (Hôte uniquement) */}
          {isHost && spotifyToken && (
            <SpotifyPlayer
              token={spotifyToken}
              trackUri={currentTrackUri}
              onPlayerReady={handlePlayerReady}
              onStateChange={handlePlayerStateChange}
              onTokenRefresh={handleTokenRefresh}
              onNext={playNextTrack}
              onPrevious={playPreviousTrack}
            />
          )}

          {/* Barre de recherche toujours visible */}
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Rechercher un titre, artiste ou album..."
                className="w-full pl-10 pr-4 py-3 bg-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
              />
              {searching && (
                <Loader2 className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 animate-spin text-purple-500" />
              )}
            </div>
            
            {/* Résultats de recherche */}
            {searchResults.length > 0 && (
              <div className="mt-4 space-y-2 max-h-96 overflow-y-auto">
                {searchResults.map((track) => (
                  <div
                    key={track.id}
                    className="flex items-center space-x-3 p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors"
                    onClick={() => addTrack(track)}
                  >
                    {track.album.images[0] && (
                      <img
                        src={track.album.images[0].url}
                        alt={track.album.name}
                        className="w-14 h-14 rounded"
                      />
                    )}
                    <div className="flex-1">
                      <h4 className="font-medium">{track.name}</h4>
                      <p className="text-sm text-gray-400">
                        {track.artists.map(a => a.name).join(', ')}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className="text-sm text-gray-400">
                        {formatDuration(track.duration_ms)}
                      </span>
                      <Button
                        size="sm"
                        className="ml-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          addTrack(track);
                        }}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Liste des pistes */}
          <div className="bg-gray-800 rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-4">File d'attente</h2>
            {session.tracks.length === 0 ? (
              <p className="text-center text-gray-400 py-8">
                Aucun titre dans la file d'attente. Recherchez et ajoutez-en un !
              </p>
            ) : (
              <div className="space-y-3">
                {session.tracks.map((track, index) => (
                  <div
                    key={track.id}
                    className={`flex items-center space-x-4 p-4 rounded-lg transition-all ${
                      track.isPlaying 
                        ? 'bg-purple-900/30 ring-2 ring-purple-500' 
                        : 'bg-gray-700/50 hover:bg-gray-700'
                    } ${track.hasPlayed ? 'opacity-50' : ''}`}
                  >
                    <span className="text-lg font-bold text-gray-400 w-8">
                      {index + 1}
                    </span>
                    {track.imageUrl && (
                      <img
                        src={track.imageUrl}
                        alt={track.title}
                        className="w-16 h-16 rounded"
                      />
                    )}
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">{track.title}</h3>
                      <p className="text-gray-400">{track.artist}</p>
                      <p className="text-sm text-gray-500">
                        Ajouté par {track.submitter.user.displayName}
                      </p>
                    </div>
                    
                    {/* Système de vote amélioré */}
                    {session.settings.votingEnabled && !track.hasPlayed && (
                      <div className="flex items-center space-x-1">
                        {VOTE_EMOJIS.map(({ emoji, value, icon: Icon, color }) => (
                          <Button
                            key={emoji}
                            size="sm"
                            variant={getUserVoteForTrack(track, emoji) ? "default" : "ghost"}
                            onClick={() => voteTrack(track.id, emoji, value)}
                            disabled={getUserVoteForTrack(track, emoji)}
                            className={`${color} hover:${color}`}
                            title={`+${value} points`}
                          >
                            <Icon className="h-4 w-4" />
                          </Button>
                        ))}
                        <span className="text-lg font-bold ml-2 min-w-[3rem] text-center">
                          {track.score}
                        </span>
                      </div>
                    )}
                    
                    <span className="text-sm text-gray-400">
                      {formatDuration(track.duration)}
                    </span>
                    
                    {/* Actions */}
                    <div className="flex items-center space-x-1">
                      {isHost && !track.isPlaying && !track.hasPlayed && (
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => playTrack(track)}
                          title="Jouer maintenant"
                          className="text-green-500 hover:text-green-400"
                        >
                          <Music className="h-4 w-4" />
                        </Button>
                      )}
                      {(isHost || track.submitter.user.displayName === user?.displayName) && !track.hasPlayed && (
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => removeTrack(track.id)}
                          title="Supprimer"
                          className="text-red-500 hover:text-red-400"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          {/* Leaderboard */}
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-4">
              <Trophy className="h-5 w-5 text-yellow-500" />
              <h2 className="text-lg font-semibold">Classement</h2>
            </div>
            <div className="space-y-2">
              {session.participants
                .sort((a, b) => b.points - a.points)
                .slice(0, 5)
                .map((participant, index) => (
                  <div
                    key={participant.id}
                    className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-lg font-bold">
                        {index === 0 && '🥇'}
                        {index === 1 && '🥈'}
                        {index === 2 && '🥉'}
                        {index > 2 && `${index + 1}.`}
                      </span>
                      {participant.user.avatarUrl && (
                        <img
                          src={participant.user.avatarUrl}
                          alt={participant.name}
                          className="w-8 h-8 rounded-full"
                        />
                      )}
                      <span className="font-medium">{participant.name}</span>
                    </div>
                    <span className="text-lg font-bold text-purple-400">
                      {participant.points} pts
                    </span>
                  </div>
                ))}
            </div>
          </div>

          {/* Statistiques de session */}
          <SessionStats session={session} />

          {/* Badges */}
          <BadgeDisplay
            userId={user?.id}
            session={session}
            compact={true}
            onBadgeUnlocked={(badge) => {
              toast({
                title: "🏆 Badge débloqué !",
                description: `Vous avez débloqué "${badge.name}"`,
              });
            }}
          />

          {/* Participants */}
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="font-semibold mb-3">Participants ({session.participants.length})</h3>
            <div className="space-y-2">
              {session.participants.map((participant) => (
                <div
                  key={participant.id}
                  className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-700/50"
                >
                  {participant.user.avatarUrl && (
                    <img
                      src={participant.user.avatarUrl}
                      alt={participant.name}
                      className="w-8 h-8 rounded-full"
                    />
                  )}
                  <span className="flex-1">{participant.name}</span>
                  {participant.user.id === session.hostId && (
                    <span className="text-xs bg-purple-600 px-2 py-1 rounded">Hôte</span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Ordre de lecture basé sur les votes */}
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="font-semibold mb-3">Prochaines pistes</h3>
            <div className="space-y-2">
              {session.tracks
                .filter(track => !track.hasPlayed && !track.isPlaying)
                .sort((a, b) => {
                  if (b.score !== a.score) {
                    return b.score - a.score;
                  }
                  return new Date(a.addedAt || 0).getTime() - new Date(b.addedAt || 0).getTime();
                })
                .slice(0, 5)
                .map((track, index) => (
                  <div key={track.id} className="flex items-center space-x-2 text-sm">
                    <span className="text-gray-400 w-4">{index + 1}.</span>
                    <div className="flex-1 truncate">
                      <p className="truncate">{track.title}</p>
                      <p className="text-gray-500 text-xs truncate">{track.artist}</p>
                    </div>
                    <span className="text-purple-400 font-bold">{track.score}</span>
                  </div>
                ))}
              {session.tracks.filter(track => !track.hasPlayed && !track.isPlaying).length === 0 && (
                <p className="text-gray-400 text-sm">Aucune piste en attente</p>
              )}
            </div>
          </div>

          {/* Info session */}
          <div className="bg-gray-800 rounded-lg p-4 text-center">
            <h3 className="font-semibold mb-3">Inviter des amis</h3>
            <div className="bg-gray-700 p-6 rounded-lg mb-3">
              <p className="text-3xl font-mono font-bold text-purple-400">
                {session.code}
              </p>
            </div>
            <p className="text-sm text-gray-400">
              Partagez ce code pour inviter des amis
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
