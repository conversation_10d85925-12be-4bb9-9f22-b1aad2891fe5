import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { env } from './config/env';
import { connectDatabase, disconnectDatabase } from './config/database';
import { connectRedis, disconnectRedis } from './config/redis';
import { errorH<PERSON>ler, notFoundHandler } from './middleware/error.middleware';
import { setupWebSocketHandlers } from './websocket';

// Import routes
import authRoutes from './routes/auth.routes';
import sessionRoutes from './routes/session.routes';
import trackRoutes from './routes/track.routes';
import playlistRoutes from './routes/playlist.routes';
import badgeRoutes from './routes/badge.routes';

// Import services
import { GamificationService } from './services/gamification.service';

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: env.FRONTEND_URL,
    credentials: true,
  },
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: env.FRONTEND_URL,
  credentials: true,
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/sessions', sessionRoutes);
app.use('/api/tracks', trackRoutes);
app.use('/api/playlists', playlistRoutes);
app.use('/api/playlist', badgeRoutes); // Note: utilise /api/playlist pour correspondre au frontend

// Error handling
app.use(notFoundHandler);
app.use(errorHandler);

// WebSocket setup
setupWebSocketHandlers(io);

// Initialize services
async function initializeServices() {
  try {
    // Initialize badges in database
    await GamificationService.initializeBadges();
    console.log('✅ Badges initialized');
  } catch (error) {
    console.error('❌ Failed to initialize services:', error);
  }
}

// Graceful shutdown
async function gracefulShutdown() {
  console.log('\n🛑 Shutting down gracefully...');
  
  httpServer.close(() => {
    console.log('✅ HTTP server closed');
  });
  
  await disconnectDatabase();
  await disconnectRedis();
  
  process.exit(0);
}

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start server
async function startServer() {
  try {
    // Connect to databases
    await connectDatabase();
    await connectRedis();
    
    // Initialize services
    await initializeServices();
    
    // Start listening
    httpServer.listen(env.PORT, () => {
      console.log(`
🎵 Playhifyy Backend Server
📡 Server running on port ${env.PORT}
🌍 Environment: ${env.NODE_ENV}
🔗 Frontend URL: ${env.FRONTEND_URL}
🔐 Spotify OAuth configured
📊 Redis connected
🗄️  PostgreSQL connected
      `);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Export io instance for use in other modules
export { io };

startServer();
