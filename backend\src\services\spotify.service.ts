import axios from 'axios';
import { env } from '../config/env';
import { redis } from '../config/redis';

export class SpotifyService {
  private static readonly SPOTIFY_API_BASE = 'https://api.spotify.com/v1';
  private static readonly SPOTIFY_ACCOUNTS_BASE = 'https://accounts.spotify.com';
  private static readonly TOKEN_CACHE_PREFIX = 'spotify:token:';
  private static readonly TOKEN_CACHE_TTL = 3500; // 58 minutes (tokens expire in 1 hour)

  /**
   * Generate Spotify OAuth authorization URL
   */
  static getAuthorizationUrl(state: string): string {
    const params = new URLSearchParams({
      client_id: env.SPOTIFY_CLIENT_ID,
      response_type: 'code',
      redirect_uri: env.SPOTIFY_REDIRECT_URI,
      state,
      scope: [
        'user-read-private',
        'user-read-email',
        'playlist-read-private',
        'playlist-modify-public',
        'playlist-modify-private',
        'user-library-read',
        'streaming',
        'user-read-playback-state',
        'user-modify-playback-state'
      ].join(' ')
    });

    return `${this.SPOTIFY_ACCOUNTS_BASE}/authorize?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access token
   */
  static async exchangeCodeForToken(code: string): Promise<{
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
  }> {
    const response = await axios.post(
      `${this.SPOTIFY_ACCOUNTS_BASE}/api/token`,
      new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: env.SPOTIFY_REDIRECT_URI,
        client_id: env.SPOTIFY_CLIENT_ID,
        client_secret: env.SPOTIFY_CLIENT_SECRET
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    return response.data;
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshAccessToken(refreshToken: string): Promise<{
    access_token: string;
    expires_in: number;
    token_type: string;
  }> {
    console.log('Attempting to refresh Spotify token...');

    try {
      const response = await axios.post(
        `${this.SPOTIFY_ACCOUNTS_BASE}/api/token`,
        new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: refreshToken,
          client_id: env.SPOTIFY_CLIENT_ID,
          client_secret: env.SPOTIFY_CLIENT_SECRET
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      console.log('Spotify token refresh successful');
      return response.data;
    } catch (error: any) {
      console.error('Spotify token refresh failed:', error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * Get user profile from Spotify
   */
  static async getUserProfile(accessToken: string): Promise<{
    id: string;
    display_name: string;
    email: string;
    images: Array<{ url: string }>;
  }> {
    const response = await axios.get(`${this.SPOTIFY_API_BASE}/me`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });

    return response.data;
  }

  /**
   * Search tracks on Spotify
   */
  static async searchTracks(
    query: string,
    accessToken: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<{
    tracks: {
      items: Array<{
        id: string;
        name: string;
        artists: Array<{ id: string; name: string }>;
        album: {
          id: string;
          name: string;
          images: Array<{ url: string; height: number; width: number }>;
        };
        duration_ms: number;
        preview_url: string | null;
        uri: string;
      }>;
      total: number;
      limit: number;
      offset: number;
    };
  }> {
    const response = await axios.get(`${this.SPOTIFY_API_BASE}/search`, {
      params: {
        q: query,
        type: 'track',
        limit,
        offset
      },
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });

    return response.data;
  }

  /**
   * Get track details
   */
  static async getTrack(
    trackId: string,
    accessToken: string
  ): Promise<{
    id: string;
    name: string;
    artists: Array<{ id: string; name: string }>;
    album: {
      id: string;
      name: string;
      images: Array<{ url: string; height: number; width: number }>;
    };
    duration_ms: number;
    preview_url: string | null;
    uri: string;
  }> {
    const response = await axios.get(`${this.SPOTIFY_API_BASE}/tracks/${trackId}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    });

    return response.data;
  }

  /**
   * Create a playlist
   */
  static async createPlaylist(
    userId: string,
    name: string,
    description: string,
    accessToken: string,
    isPublic: boolean = true
  ): Promise<{
    id: string;
    name: string;
    description: string;
    uri: string;
    external_urls: { spotify: string };
  }> {
    const response = await axios.post(
      `${this.SPOTIFY_API_BASE}/users/${userId}/playlists`,
      {
        name,
        description,
        public: isPublic
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data;
  }

  /**
   * Add tracks to playlist
   */
  static async addTracksToPlaylist(
    playlistId: string,
    trackUris: string[],
    accessToken: string
  ): Promise<{ snapshot_id: string }> {
    const response = await axios.post(
      `${this.SPOTIFY_API_BASE}/playlists/${playlistId}/tracks`,
      {
        uris: trackUris
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data;
  }

  /**
   * Cache access token in Redis
   */
  static async cacheAccessToken(userId: string, accessToken: string): Promise<void> {
    await redis.setex(
      `${this.TOKEN_CACHE_PREFIX}${userId}`,
      this.TOKEN_CACHE_TTL,
      accessToken
    );
  }

  /**
   * Get cached access token from Redis
   */
  static async getCachedAccessToken(userId: string): Promise<string | null> {
    return await redis.get(`${this.TOKEN_CACHE_PREFIX}${userId}`);
  }

  /**
   * Clear cached access token
   */
  static async clearCachedAccessToken(userId: string): Promise<void> {
    await redis.del(`${this.TOKEN_CACHE_PREFIX}${userId}`);
  }
}
