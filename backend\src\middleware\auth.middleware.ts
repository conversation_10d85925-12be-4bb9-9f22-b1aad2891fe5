import { Request, Response, NextFunction } from 'express';
import { verifyToken, JWTPayload } from '../utils/auth';

export interface AuthRequest extends Request {
  user?: JWTPayload;
}

export function authMiddleware(req: AuthRequest, res: Response, next: NextFunction): void {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'No token provided' });
      return;
    }
    
    const token = authHeader.substring(7);
    const payload = verifyToken(token);
    
    req.user = payload;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
    return;
  }
}

export function requireHost(req: AuthRequest, res: Response, next: NextFunction): void {
  if (!req.user || req.user.role !== 'HOST') {
    res.status(403).json({ error: 'Host access required' });
    return;
  }
  next();
}

export function requireParticipant(req: AuthRequest, res: Response, next: NextFunction): void {
  if (!req.user || (req.user.role !== 'PARTICIPANT' && req.user.role !== 'HOST')) {
    res.status(403).json({ error: 'Participant access required' });
    return;
  }
  next();
}
