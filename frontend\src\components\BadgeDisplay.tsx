import { useState, useEffect } from 'react';
import { Trophy, Star, Award, Lock } from 'lucide-react';
import { Badge, BadgeService } from '@/services/badge.service';

interface BadgeDisplayProps {
  userId?: string;
  session?: any;
  compact?: boolean;
  onBadgeUnlocked?: (badge: Badge) => void;
}

export function BadgeDisplay({ userId, session, compact = false, onBadgeUnlocked }: BadgeDisplayProps) {
  const [userBadges, setUserBadges] = useState<Badge[]>([]);
  const [availableBadges, setAvailableBadges] = useState<Badge[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadBadges();
  }, [userId]);

  useEffect(() => {
    if (session && userId) {
      checkForNewBadges();
    }
  }, [session, userId]);

  const loadBadges = async () => {
    try {
      setLoading(true);

      // Créer la liste des badges disponibles même si l'API échoue
      const allBadges = Object.entries(BadgeService.BADGES).map(([id, badge]) => ({
        id,
        name: badge.name,
        description: badge.description,
        icon: badge.icon,
        points: 50,
        criteria: badge.criteria,
        unlockedAt: undefined,
      }));

      setAvailableBadges(allBadges);

      // Essayer de charger les stats utilisateur
      if (userId) {
        try {
          const stats = await BadgeService.getUserStats(userId);
          setUserBadges(stats.badges);

          // Mettre à jour les badges avec les dates de déblocage
          const updatedBadges = allBadges.map(badge => ({
            ...badge,
            unlockedAt: stats.badges.find(b => b.id === badge.id)?.unlockedAt,
          }));
          setAvailableBadges(updatedBadges);
        } catch (error) {
          console.warn('Could not load user badge stats:', error);
          // Continuer avec les badges par défaut
        }
      }
    } catch (error) {
      console.error('Error loading badges:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkForNewBadges = async () => {
    if (!session || !userId) return;

    try {
      // Vérification locale des badges basée sur les données de session
      const sessionStats = BadgeService.calculateSessionStats(session, userId);

      // Créer des stats utilisateur basiques à partir de la session
      const basicStats = {
        userId,
        totalTracks: sessionStats.userTracks.length,
        totalVotes: 0, // À calculer si nécessaire
        totalPoints: 0, // À calculer si nécessaire
        sessionsHosted: session.hostId === userId ? 1 : 0,
        sessionsJoined: 1,
        tracksPlayed: sessionStats.userTracks.filter((t: any) => t.hasPlayed).length,
        badges: userBadges,
      };

      const newBadges = BadgeService.checkLocalBadges(basicStats, sessionStats);

      if (newBadges.length > 0) {
        // Mettre à jour les badges localement
        setUserBadges(prev => [...prev, ...newBadges]);

        // Notifier les nouveaux badges
        newBadges.forEach(badge => {
          if (onBadgeUnlocked) {
            onBadgeUnlocked(badge);
          }
        });

        // Essayer de synchroniser avec le serveur (optionnel)
        try {
          await BadgeService.checkBadges();
        } catch (error) {
          console.warn('Could not sync badges with server:', error);
          // Continuer sans synchronisation serveur
        }
      }
    } catch (error) {
      console.warn('Error checking for new badges:', error);
      // Ne pas bloquer l'interface pour les erreurs de badges
    }
  };

  const isUnlocked = (badgeId: string) => {
    return userBadges.some(badge => badge.id === badgeId);
  };

  const getProgressPercentage = (badge: Badge) => {
    if (!session || !userId) return 0;
    
    const stats = BadgeService.calculateSessionStats(session, userId);
    
    switch (badge.criteria.type) {
      case 'tracks_added':
        return Math.min(100, (stats.userTracks.length / badge.criteria.threshold) * 100);
      case 'track_score':
        const maxScore = Math.max(...stats.userTracks.map((t: any) => t.score), 0);
        return Math.min(100, (maxScore / badge.criteria.threshold) * 100);
      case 'positive_streak':
        return Math.min(100, (stats.positiveStreak / badge.criteria.threshold) * 100);
      default:
        return isUnlocked(badge.id) ? 100 : 0;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (compact) {
    // Version compacte pour la sidebar
    const recentBadges = userBadges.slice(-3);
    
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="font-semibold mb-3 flex items-center">
          <Trophy className="h-5 w-5 mr-2 text-yellow-400" />
          Badges récents
        </h3>
        {recentBadges.length > 0 ? (
          <div className="flex space-x-2">
            {recentBadges.map((badge) => (
              <div
                key={badge.id}
                className="flex-1 bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-2 text-center"
                title={badge.description}
              >
                <div className="text-lg mb-1">{badge.icon}</div>
                <p className="text-xs font-medium truncate">{badge.name}</p>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-400 text-sm">Aucun badge débloqué</p>
        )}
        <div className="mt-3 text-center">
          <p className="text-xs text-gray-400">
            {userBadges.length} / {availableBadges.length} badges
          </p>
        </div>
      </div>
    );
  }

  // Version complète
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold flex items-center">
        <Award className="h-6 w-6 mr-2 text-purple-400" />
        Badges & Succès
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {availableBadges.map((badge) => {
          const unlocked = isUnlocked(badge.id);
          const progress = getProgressPercentage(badge);
          
          return (
            <div
              key={badge.id}
              className={`relative p-4 rounded-lg border transition-all ${
                unlocked
                  ? 'bg-yellow-900/20 border-yellow-500/50 shadow-lg'
                  : 'bg-gray-800/50 border-gray-600/50'
              }`}
            >
              {!unlocked && (
                <div className="absolute top-2 right-2">
                  <Lock className="h-4 w-4 text-gray-500" />
                </div>
              )}
              
              <div className="text-center">
                <div className={`text-3xl mb-2 ${unlocked ? '' : 'grayscale opacity-50'}`}>
                  {badge.icon}
                </div>
                <h3 className={`font-semibold ${unlocked ? 'text-yellow-400' : 'text-gray-400'}`}>
                  {badge.name}
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  {badge.description}
                </p>
                
                {!unlocked && progress > 0 && (
                  <div className="mt-3">
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">
                      {Math.round(progress)}% complété
                    </p>
                  </div>
                )}
                
                {unlocked && badge.unlockedAt && (
                  <p className="text-xs text-yellow-500 mt-2">
                    Débloqué le {new Date(badge.unlockedAt).toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
