import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface GuestJoinResponse {
  session: any;
  participant: any;
  guestId: string;
  isGuest: true;
}

export class GuestService {
  /**
   * Join a session as guest
   */
  static async joinSession(code: string, guestName: string): Promise<GuestJoinResponse> {
    const response = await axios.post(`${API_URL}/sessions/join-guest`, {
      code: code.toUpperCase(),
      guestName,
    });
    return response.data;
  }

  /**
   * Get session details as guest
   */
  static async getSession(code: string, guestId: string): Promise<any> {
    const response = await axios.get(`${API_URL}/sessions/guest/${code.toUpperCase()}`, {
      params: { guestId },
    });
    return response.data;
  }

  /**
   * Store guest info in localStorage
   */
  static storeGuestInfo(guestId: string, sessionCode: string, guestName: string): void {
    localStorage.setItem('guestInfo', JSON.stringify({
      guestId,
      sessionCode: sessionCode.toUpperCase(),
      guestName,
      isGuest: true,
      joinedAt: new Date().toISOString(),
    }));
  }

  /**
   * Get guest info from localStorage
   */
  static getGuestInfo(): {
    guestId: string;
    sessionCode: string;
    guestName: string;
    isGuest: boolean;
    joinedAt: string;
  } | null {
    const stored = localStorage.getItem('guestInfo');
    if (!stored) return null;
    
    try {
      return JSON.parse(stored);
    } catch {
      return null;
    }
  }

  /**
   * Clear guest info from localStorage
   */
  static clearGuestInfo(): void {
    localStorage.removeItem('guestInfo');
  }

  /**
   * Check if user is currently a guest
   */
  static isGuest(): boolean {
    const guestInfo = this.getGuestInfo();
    return guestInfo?.isGuest === true;
  }
}
